const { SOCIAL_MEDIA_LINKS } = require('../../constants/constant');
const { getTranslation } = require("../../helpers/helpers");
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const contactUsEmail = process.env.MAIL_CONTACT_USER;
const phoneNumber = process.env.PHONE_NUMBER;
const FRONT_URL = process.env.FRONTEND_URL;

exports.getVerificationAccountTemplate = (req, site_name, account_activation_link, website_url) => {
    return `
        <div dir="${req.locale === 'ar' ? 'rtl' : 'ltr'}"
            style="max-width: 560px; padding: 20px; background: #ffffff; border-radius: 5px; margin: 0px auto; font-family: Open Sans, Helvetica, Arial; font-size: 16px; color: #666; text-align: ${req.locale === 'ar' ? 'right' : 'left'};">
            <div class="header" style="text-align: center; padding-bottom: 40px;">
                <a href="${website_url}/${req.locale}" target="_blank">
                    <img src="${website_url}/images/home/<USER>" alt="ZTechEngineering" style="height: 60px; display: block; margin: 0 auto; border: 0;">
                </a>
            </div>
            <div style="padding: 0 30px 30px 30px; border-bottom: 3px solid #eeeeee; text-align: ${req.locale === 'ar' ? 'right' : 'left'};">
                <div style="padding: 30px 0; font-size: 16px; text-align: center; line-height: 30px;">
                    ${req.t('activation_message1')}
                </div>
                <div style="padding: 10px 0 50px 0; text-align: center;">
                    <a style="background: #497ef7; color: #fff; padding: 12px 30px; text-decoration: none; border-radius: 3px; letter-spacing: 0.3px;" href="${account_activation_link}" target="_blank" rel="noopener">
                        ${req.t('activate_account')}
                    </a>
                </div>
                <div style="padding: 30px 0; font-size: 16px; text-align: center; line-height: 30px;">
                    ${req.t('activation_message2')}
                </div>
                <div style="padding: 15px; background: #eee; border-radius: 3px; text-align: center;">
                    ${req.t('need_help')}
                    <a style="color: #497ef7; text-decoration: none;" href="mailto:<EMAIL>">
                        ${req.t('contact_us')}
                    </a>
                </div>
            </div>
            <div class="footer" style="text-align: center; padding: 20px; background: #f9f9f9; border-top: 1px solid #eeeeee; margin-top: 100px;">
                <strong>${req.t('socialMedia')}</strong>
                <div class="social-icons" style="padding: 10px 0;">
                ${Object.values(SOCIAL_MEDIA_LINKS).map(
        (link) => `
                <a href="${link.url}" target="_blank" style="margin: 0 5px;">
                <img alt="${link.title}" src="${website_url}${link.icon}" title="${link.title}" width="25" style="vertical-align: middle;">
                </a>`
    ).join('')}
                </div>
                <div class="footer-content" style="padding-top: 10px; text-align: center">
                    <strong>${req.t('whereToFindUs')}</strong>
                    <p>${req.t('contactEmail')} <a href="mailto:<EMAIL>" style="color: #497ef7; text-decoration: none;"><EMAIL></a></p>
                    <p>${req.t('ZTechEngineeringAddress')}</p>
                    <p>${req.t('rightsReserved')}</p>
                </div>
            </div>
        </div>
    `;
};

exports.getForgotPasswordTemplate = (req, site_name, account_activation_link, website_url) => {
    return `
    <div dir="${req.locale === 'ar' ? 'rtl' : 'ltr'}"
    style="max-width: 560px; padding: 20px; background: #ffffff; border-radius: 5px; margin: 0 auto; font-family: Open Sans, Helvetica, Arial; font-size: 16px; color: #666; text-align: ${req.locale === 'ar' ? 'right' : 'left'};">

    <div class="header" style="text-align: center; padding-bottom: 40px;">
    <a href="${website_url}/${req.locale}" target="_blank">
                <img src="${website_url}/images/home/<USER>" alt="${site_name}" style="height: 60px; display: block; margin: 0 auto; border: 0;">
            </a>
        </div>

        <div style="padding: 0 30px 30px 30px; border-bottom: 3px solid #eeeeee; text-align: ${req.locale === 'ar' ? 'right' : 'left'};">
            <div style="padding: 30px 0; font-size: 16px; text-align: center; line-height: 30px;">
                ${getTranslation('forgot_password_message1', req.locale)}
            </div>
            <div style="padding: 10px 0 50px 0; text-align: center;">
                <a style="background: #497ef7; color: #fff; padding: 12px 30px; text-decoration: none; border-radius: 3px; letter-spacing: 0.3px;"
                    href="${account_activation_link}" target="_blank" rel="noopener">
                    ${getTranslation('reset_password', req.locale)}
                </a>
            </div>
            <div style="padding: 30px 0; font-size: 16px; text-align: center; line-height: 30px;">
                ${getTranslation('forgot_password_message2', req.locale)}
            </div>
            <div style="padding: 15px; background: #eee; border-radius: 3px; text-align: center;">
                ${req.t('need_help')}
                <a style="color: #497ef7; text-decoration: none;" href="mailto:<EMAIL>">
                    ${getTranslation('contact_us', req.locale)}
                </a>
            </div>
        </div>

        <div class="footer" style="text-align: center; padding: 20px; background: #f9f9f9; border-top: 1px solid #eeeeee; margin-top: 100px;">
            <strong>${getTranslation('socialMedia', req.locale)}</strong>
            <div class="social-icons" style="padding: 10px 0;">
                ${Object.values(SOCIAL_MEDIA_LINKS)
            .map(
                (link) => `
                            <a href="${link.url}" target="_blank" style="margin: 0 5px;">
                                <img alt="${link.title}" src="${website_url}${link.icon}" title="${link.title}" width="25" style="vertical-align: middle;">
                            </a>`
            )
            .join('')}
            </div>
            <div class="footer-content" style="padding-top: 10px; text-align: center;">
                <strong>${getTranslation('whereToFindUs', req.locale)}</strong>
                <p>${getTranslation('contactEmail', req.locale)} <a href="mailto:<EMAIL>" style="color: #497ef7; text-decoration: none;"><EMAIL></a></p>
                <p>${getTranslation('ZTechEngineeringAddress', req.locale)}</p>
                <p>${getTranslation('rightsReserved', req.locale)}</p>
            </div>
        </div>
    </div>`;
};

exports.getPasswordResetSuccessfully = (userData) => {
    let lang = userData.favoriteLang;

    let emailHtml = `
<!DOCTYPE html>
<html lang="${lang}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${getTranslation('passwordReset', lang)}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: ${lang === 'ar' ? 'rtl' : 'ltr'};
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
            font-size: 14px;
            text-align: ${lang === 'ar' ? 'right' : 'left'};
            direction: ${lang === 'ar' ? 'rtl' : 'ltr'};
        }

        .logo {
            text-align: center;
            margin-bottom: 20px;
        }

        .logo img {
            max-width: 150px;
            height:auto;
        }

        h1 {
            color: #5cb85c;
            text-align: center;
            font-size: 22px;
        }

        p {
            margin: 0 0 10px;
            font-size: 14px;
            text-align: ${lang === 'ar' ? 'right' : 'left'};
        }

        a {
            color: #337ab7;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        .footer {
            margin-top: 20px;
            text-align: center;
            color: #777;
            font-size: 12px;
            margin-bottom: 10px;
        }

        .social-icons img {
            display: inline-block;
            margin: 0 10px;
            height: auto;
            border: 0;
        }

        .footer-content {
            font-size: 12px;
            line-height: 150%;
            color: #777777;
            margin-top: 20px;
            text-align: center !important;
        }

        .footer-content a {
            color: #4CAF50;
            text-decoration: none;
        }
        .footer-content p {
            text-align: center !important;
        }

        .footer-content a:hover {
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="logo" style="text-align: center; max-width: 150px; margin: 0 auto;">
            <a href="${FRONT_URL}/${lang}" style="outline:none" tabindex="-1" target="_blank">
                <img alt="odhiya" height="auto" src="${FRONT_URL}/images/favicon.png"
                style="display: block; height: auto; border: 0; width: 100%;" title="odhiya" width="150" />
            </a>
        </div>
        <h1>${getTranslation('passwordReset', lang)}</h1>
        <p>${getTranslation('dear', lang)} ${userData.firstName} ${userData.lastName},</p>
        <p>${getTranslation('passwordResetSuccess', lang)}</p>
        <p>${getTranslation('ifNotYou', lang)}</p>
        <p>${getTranslation('contactSupport', lang)} <a href="mailto:${contactUsEmail}">${contactUsEmail}</a>.</p>
        <p>${getTranslation('bestRegards', lang)}</p>
        <p>${getTranslation('team', lang)}</p>
        <div class="footer">
            <strong>${getTranslation('socialMedia', lang)}</strong>
            <div class="social-icons">
                <a href="https://www.facebook.com/Odhiya.1/" target="_blank">
                    <img alt="Facebook" src="${FRONT_URL}/images/facebookIcon.png" title="Facebook" width="25" />
                </a>
                <a href="https://www.instagram.com/odhiya.1/" target="_blank">
                    <img alt="Instagram" src="${FRONT_URL}/images/instagramIcon.png" title="Instagram" width="25" />
                </a>
                <a href="https://www.tiktok.com/@odhiya1/" target="_blank">
                    <img alt="tiktok" src="${FRONT_URL}/images/tikTokIcon.png" title="tiktok" width="25" />
                </a>
                <a href="https://www.youtube.com/@Odhiya/" target="_blank">
                    <img alt="Youtube" src="${FRONT_URL}/images/ytbIcon.png" title="Youtube" width="25" />
                </a>
            </div>
            <div class="footer-content">
                <strong>${getTranslation('whereToFindUs', lang)}</strong>
                <p>${getTranslation('contactUs', lang)} <a href="mailto:${contactUsEmail}">${contactUsEmail}</a></p>
                <p>${getTranslation('odhiaAddress', lang)}</p>
                <p>${getTranslation('rightsReserved', lang)}</p>
            </div>
        </div>
    </div>
</body>

</html>
    `;
    return emailHtml;
};

exports.getEmailOtpVerificationTemplate = (lang, otpCode) => {
    const site_name = 'ZTechEngineering';
    const website_url = 'https://ztechengineering.com';

    return `
    <div style="max-width: 560px; margin: 0 auto; background: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); font-family: 'Open Sans', Helvetica, Arial, sans-serif; color: #333; padding: 20px;" dir="${lang === 'ar' ? 'rtl' : 'ltr'}">
        <div style="text-align: center; padding-bottom: 20px;">
            <a href="${website_url}/${lang}" target="_blank" style="text-decoration: none;">
                <img src="${website_url}/images/home/<USER>" alt="${site_name}" style="height: 60px; display: block; margin: 0 auto;">
            </a>
        </div>

        <div style="padding: 20px 30px; border-bottom: 2px solid #eee; text-align: ${lang === 'ar' ? 'right' : 'left'};">
            <h1 style="font-size: 24px; color: #333; margin-bottom: 10px;">${getTranslation('email_verification', lang)}</h1>
            <p style="line-height: 1.6; margin-bottom: 20px; color: #333;">${getTranslation('dearUser', lang)},</p>
            <p style="line-height: 1.6; margin-bottom: 20px; color: #333;">${getTranslation('explain_why', lang)}.</p>
            <div style="background: #f7fdf9; border: 1px solid #54b175; border-radius: 8px; text-align: center; padding: 15px; margin: 20px 0;">
                <p style="color: #497ef7; font-size: 28px; font-weight: bold; margin: 0;">${otpCode}</p>
            </div>
            <p style="line-height: 1.6;">${getTranslation('otp_expiration_message', lang)}</p>
        </div>
        <div style="text-align: center; padding: 20px; background: #f9f9f9; border-top: 1px solid #eee;">
            <strong style="display: block; margin-bottom: 10px;">${getTranslation('socialMedia', lang)}</strong>
            <div style="padding: 10px 0;">
                ${Object.values(SOCIAL_MEDIA_LINKS)
            .map(
                (link) => `
                        <a href="${link.url}" target="_blank" style="margin: 0 8px; display: inline-block;">
                            <img alt="${link.title}" src="${website_url}${link.icon}" title="${link.title}" style="width: 25px; vertical-align: middle;">
                        </a>`
            )
            .join('')}
            </div>
            <div style="padding-top: 10px;">
                <p style="margin: 10px 0; font-size: 14px; color: black;">
                    <strong>${getTranslation('whereToFindUs', lang)}</strong>
                </p>
                <p style="margin: 10px 0; font-size: 14px;">
                    ${getTranslation('contactEmail', lang)}
                    <a href="mailto:<EMAIL>" style="color: #497ef7; text-decoration: none;">
                        <EMAIL>
                    </a>
                </p>
                <p style="margin: 10px 0; font-size: 14px;">${getTranslation('ZTechEngineeringAddress', lang)}</p>
                <p style="margin: 10px 0; font-size: 14px;">${getTranslation('rightsReserved', lang)}</p>
            </div>
        </div>
    </div>`;
};

exports.getOrderSummaryTemplate = (order, lang) => {
    let summaryHTML = '';
    summaryHTML += `
<!DOCTYPE html>
<html lang="${lang}" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:v="urn:schemas-microsoft-com:vml">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${getTranslation('orderSummary', lang)}</title>
</head>
<body style="background-color: #f4f4f4; margin: 0; padding: 0; direction: ${lang === 'ar' ? 'rtl' : 'ltr'}; text-align: ${lang === 'ar' ? 'right' : 'left'};">
    <div style="width: 100% !important; max-width: 800px; margin: 0 auto; background-color: #ffffff; font-family: 'Montserrat', sans-serif; padding: 20px;">
        <div style="background-color: #ffffff; text-align: center; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);">
            <div style="text-align: center; max-width: 150px; margin: 0 auto;">
                <a href="${FRONT_URL}/${lang}" style="outline:none" tabindex="-1" target="_blank">
                    <img alt="ztechengineering" height="auto" src="${FRONT_URL}/images/home/<USER>" style="display: block; height: auto; border: 0; width: 100%;" title="odhiya" width="180" />
                </a>
            </div>
            <h1 style="color: #05144b; font-size: 30px; margin: 10px 0;">${getTranslation('orderSummary', lang)}</h1>
            <p style="color: #05144b; font-size: 16px; line-height: 1.6; margin: 10px 20px;">${getTranslation('tnxForUrTrust', lang)}</p>
        </div>
        <div style="font-size: 14px; direction: ${lang === 'ar' ? 'rtl' : 'ltr'}; text-align: ${lang === 'ar' ? 'right' : 'left'}; margin-top: 20px;">
            <p>${getTranslation('dear', lang)} ${order.billingInfo.BillToName},</p>
            <p>${getTranslation('emailOrderSummary', lang)}</p>
            <p>${getTranslation('bestRegards', lang)},</p>
            <p>ZTechEngineering</p>
        </div>
        <div style="background-color: #ffffff; padding: 20px; margin: 10px 0; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);">
            <div style="display: block; text-align: ${lang === 'ar' ? 'right' : 'left'}; direction: ${lang === 'ar' ? 'rtl' : 'ltr'};">
                <h2 style="font-size: 18px; margin-bottom: 10px; color: #333333;">${getTranslation('orderDetails', lang)}</h2>
                <br/>
                <p style="direction: ${lang === 'ar' ? 'rtl' : 'ltr'}; font-size: 14px;"><strong>${getTranslation('orderId', lang)}:</strong>${order._id}</p>
            </div>

            ${order.subOrders.map(item => `
            <div style="background-color: #f2f2f2; color: #333333; padding: 10px; margin-bottom: 10px; border-radius: 10px; overflow: hidden;">
                <div>
                    <p style="direction: ${lang === 'ar' ? 'rtl' : 'ltr'}; font-size: 14px;"><strong>${getTranslation('itemId', lang)}:</strong> ${item._id}</p>
                </div>

                <div style="background-color: #ffffff; color: #333333; margin-bottom: 10px; border-radius: 10px; overflow: hidden; display: flex; align-items: center; justify-content:space-between; width: 100%;">
                    <div style="padding: 10px; flex: 1; font-size: 14px; direction: ${lang === 'ar' ? 'rtl' : 'ltr'};">
                        <div><strong>${getTranslation('name', lang)}:</strong> ${item.package.name}</div>
                        <div><strong>${getTranslation('price', lang)}:</strong> ${item.package.price} ${getTranslation('MAD', lang)}</div>
                        <div><strong>${getTranslation('quantity', lang)}:</strong> ${item.quantity}</div>
                        <div><strong>${getTranslation('status', lang)}:</strong> ${item.status}</div>
                        <div><strong>${getTranslation('createdAt', lang)}:</strong> ${new Date(item.createdAt).toLocaleDateString(lang)}</div>
                    </div>
                </div>
            </div>
            `).join('')}
            <table style="border: 1px solid #000000; border-collapse: collapse; width: 100%; max-width: 600px; margin: 0 auto; margin-top: 20px; direction: ${lang === 'ar' ? 'rtl' : 'ltr'};">
                <thead>
                    <tr>
                        <th style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px; background-color: #dddddd;">${getTranslation('summary', lang)}</th>
                        <th style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px; background-color: #dddddd;">${getTranslation('amount', lang)}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>${getTranslation('subTotal', lang)}:</strong></td>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>${order.subTotal?.toFixed(2) || "ERR "} ${getTranslation('MAD', lang)}</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>${getTranslation('totalDiscount', lang)}:</strong></td>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>-${order.totalDiscount?.toFixed(2) || 0} ${getTranslation('MAD', lang)}</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>${getTranslation('tax', lang)}:</strong></td>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>+${order.taxAmount?.toFixed(2)} MAD (${order.taxRate * 100}%)</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>${getTranslation('orderTotal', lang)}:</strong></td>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>${order.totalPrice?.toFixed(2)} ${getTranslation('MAD', lang)}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div style="text-align: center; padding: 20px; background: #f9f9f9; border-top: 1px solid #eeeeee; margin-top: 20px;">
            <strong>${getTranslation('socialMedia', lang)}</strong>
            <div style="padding: 10px 0;">
                ${Object.values(SOCIAL_MEDIA_LINKS).map(
        (link) => `
                            <a href="${link.url}" target="_blank" style="margin: 0 5px;">
                                <img alt="${link.title}" src="${FRONT_URL}${link.icon}" title="${link.title}" width="25" style="vertical-align: middle;">
                            </a>`
    )
            .join('')}
            </div>
            <div style="padding-top: 10px; text-align: center;">
                <strong>${getTranslation('whereToFindUs', lang)}</strong>
                <p>${getTranslation('contactEmail', lang)} <a href="mailto:<EMAIL>" style="color: #497ef7; text-decoration: none;"><EMAIL></a></p>
                <p>${getTranslation('ZTechEngineeringAddress', lang)}</p>
                <p>${getTranslation('rightsReserved', lang)}</p>
            </div>
        </div>
    </div>
</body>
</html>
`;
    return summaryHTML;
}

exports.getNewOrderAdminAlertTemp = (order, lang) => {
    let summaryHTML = '';
    summaryHTML += `
<!DOCTYPE html>
<html lang="${lang}" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:v="urn:schemas-microsoft-com:vml">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Order Alert</title>
</head>
<body style="background-color: #f4f4f4; margin: 0; padding: 0; direction: ${lang === 'ar' ? 'rtl' : 'ltr'}; text-align: ${lang === 'ar' ? 'right' : 'left'};">
    <div style="width: 100% !important; max-width: 800px; margin: 0 auto; background-color: #ffffff; font-family: 'Montserrat', sans-serif; padding: 20px;">
        <div style="background-color: #ffffff; text-align: center; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);">
            <div style="text-align: center; max-width: 150px; margin: 0 auto;">
                <a href="${FRONT_URL}/${lang}" style="outline:none" tabindex="-1" target="_blank">
                    <img alt="ztechengineering" height="auto" src="${FRONT_URL}/images/home/<USER>" style="display: block; height: auto; border: 0; width: 100%;" title="odhiya" width="180" />
                </a>
            </div>
            <h1 style="color: #05144b; font-size: 30px; margin: 10px 0;">New Order Alert</h1>
            <p style="color: #05144b; font-size: 16px; line-height: 1.6; margin: 10px 20px;">A new order has been created.</p>
        </div>
        <div style="font-size: 14px; direction: ${lang === 'ar' ? 'rtl' : 'ltr'}; text-align: ${lang === 'ar' ? 'right' : 'left'}; margin-top: 20px;">
            <p>Dear Admin,</p>
            <p>Here are the details of the new order:</p>
            <p><strong>Customer Name:</strong> ${order.user.firstName} ${order.user.lastName}</p>
            <p><strong>Customer Email:</strong> ${order.user.email}</p>
            <p>Best regards,</p>
            <p>ZTechEngineering</p>
        </div>
        <div style="background-color: #ffffff; padding: 20px; margin: 10px 0; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);">
            <div style="display: block; text-align: ${lang === 'ar' ? 'right' : 'left'}; direction: ${lang === 'ar' ? 'rtl' : 'ltr'};">
                <h2 style="font-size: 18px; margin-bottom: 10px; color: #333333;">Order Details</h2>
                <br/>
                <p style="direction: ${lang === 'ar' ? 'rtl' : 'ltr'}; font-size: 14px;"><strong>Order ID:</strong>${order._id}</p>
            </div>

            ${order.subOrders.map(item => `
            <div style="background-color: #f2f2f2; color: #333333; padding: 10px; margin-bottom: 10px; border-radius: 10px; overflow: hidden;">
                <div>
                    <p style="direction: ${lang === 'ar' ? 'rtl' : 'ltr'}; font-size: 14px;"><strong>Item ID:</strong> ${item._id}</p>
                </div>

                <div style="background-color: #ffffff; color: #333333; margin-bottom: 10px; border-radius: 10px; overflow: hidden; display: flex; align-items: center; justify-content:space-between; width: 100%;">
                    <div style="padding: 10px; flex: 1; font-size: 14px; direction: ${lang === 'ar' ? 'rtl' : 'ltr'};">
                        <div><strong>Name:</strong> ${item.package.name}</div>
                        <div><strong>Description:</strong> ${item.package.description}</div>
                        <div><strong>Price:</strong> ${item.package.price?.toFixed(2)} MAD</div>
                        <div><strong>Quantity:</strong> ${item.quantity}</div>
                        <div><strong>Status:</strong> ${item.status}</div>
                        <div><strong>Created At:</strong> ${new Date(item.createdAt).toLocaleDateString(lang)}</div>
                    </div>
                </div>
            </div>
            `).join('')}
            <table style="border: 1px solid #000000; border-collapse: collapse; width: 100%; max-width: 600px; margin: 0 auto; margin-top: 20px; direction: ${lang === 'ar' ? 'rtl' : 'ltr'};">
                <thead>
                    <tr>
                        <th style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px; background-color: #dddddd;">Summary</th>
                        <th style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px; background-color: #dddddd;">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>Sub Total:</strong></td>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>${order.subTotal?.toFixed(2) || "ERR"} MAD</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>Total Discount:</strong></td>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>-${order.totalDiscount?.toFixed(2) || "ERR"} MAD</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>Tax:</strong></td>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>+${order.taxAmount?.toFixed(2)} MAD (${order.taxRate * 100}%)</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>Order Total:</strong></td>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>${order.totalPrice?.toFixed(2)} MAD</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div style="text-align: center; padding: 20px; background: #f9f9f9; border-top: 1px solid #eeeeee; margin-top: 20px;">
            <strong>Follow Us</strong>
            <div style="padding: 10px 0;">
                ${Object.values(SOCIAL_MEDIA_LINKS)
            .map(
                (link) => `
                            <a href="${link.url}" target="_blank" style="margin: 0 5px;">
                                <img alt="${link.title}" src="${FRONT_URL}${link.icon}" title="${link.title}" width="25" style="vertical-align: middle;">
                            </a>`
            )
            .join('')}
            </div>
            <div style="padding-top: 10px; text-align: center;">
                <strong>Where to Find Us</strong>
                <p>Contact Email: <a href="mailto:<EMAIL>" style="color: #497ef7; text-decoration: none;"><EMAIL></a></p>
                <p>ZTechEngineering Address</p>
                <p>All rights reserved</p>
            </div>
        </div>
    </div>
</body>
</html>
`;
    return summaryHTML;
}

exports.getBillingSummary = (order) => {
    let lang = order.customer.favoriteLang;

    let summaryHTML = `
<!DOCTYPE html>
<html lang="${lang}">
<head>
    <title>${getTranslation('paymentConfirmation', lang)}</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            background-color: #ffffff;
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        .container {
            width: 100%;
            max-width: 750px;
            margin: 0 auto;
            padding: 15px;
        }
        .header {
            background-color: #54b175;
            text-align: center;
            padding: 20px 0;
            border-radius: 1rem;
            color:white;
        }
        .header .logo img {
            max-width: 150px;
            height: auto;
        }
        .header h1 {
            font-size: 30px;
            margin: 10px 0;
        }
        .header p {
            font-size: 14px;
        }
        .content {
            padding: 20px;
            text-align: ${lang === 'ar' ? 'right' : 'left'};
        }
        .content p {
            margin: 10px 0;
            font-size: 14px;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #777;
            font-size: 12px;
            margin-bottom: 10px;
        }
        .footer-content {
            font-size: 12px;
            line-height: 150%;
            color: #777777;
            margin-top: 10px;
            text-align: center;
        }
        .social-icons {
            margin-top: 10px;
        }
        .social-icons a {
            margin: 0 5px;
        }
    </style>
</head>
<body dir="${lang === 'ar' ? 'rtl' : 'ltr'}">
    <div class="container">
        <div class="header">
            <div class="logo" style="text-align: center; max-width: 150px; margin: 0 auto;">
                <a href="${FRONT_URL}/${lang}" style="outline:none" tabindex="-1" target="_blank">
                    <img alt="odhiya" height="auto" src="${FRONT_URL}/images/favicon.png"
                    style="display: block; height: auto; border: 0; width: 100%;" title="odhiya" width="150" />
                </a>
            </div>
            <h1>${getTranslation('paymentConfirmation', lang)}</h1>
            <p>${getTranslation('tnxForUrTrust', lang)}</p>
        </div>
        <div class="content">
            <p>${getTranslation('dear', lang)} ${order.billing.BillToName},</p>
            <p>${getTranslation('paymentProcessed', lang)}</p>
            <p>${getTranslation('orderAmount', lang)}: ${order.totalAmount}${getTranslation('MAD', lang)}</p>
            <p>${getTranslation('orderID', lang)}: ${order.reference}</p>
            <p>${getTranslation('transactionId', lang)}: ${order.transactionId}</p>
            <p>${getTranslation('paymentMethod', lang)}: CMI</p>
            <p>${getTranslation('transactionDate', lang)}: ${new Date(order.datePaid).toLocaleDateString('fr')}</p>
            <p>${getTranslation('thankYouPurchase', lang)}</p>
        </div>
        <div class="footer">
        <strong>${getTranslation('socialMedia', lang)}</strong>
        <div class="social-icons">
            <a href="https://www.facebook.com/Odhiya.1/" target="_blank">
                <img src="${FRONT_URL}/images/facebookIcon.png" alt="Facebook" title="Facebook" width="25" style="border: 0; height: auto;">
            </a>
            <a href="https://www.instagram.com/odhiya.1/" target="_blank">
                <img src="${FRONT_URL}/images/instagramIcon.png" alt="Instagram" title="Instagram" width="25" style="border: 0; height: auto;">
            </a>
            <a href="https://www.tiktok.com/@odhiya1/" target="_blank">
                <img src="${FRONT_URL}/images/tikTokIcon.png" alt="TikTok" title="TikTok" width="25" style="border: 0; height: auto;">
            </a>
            <a href="https://www.youtube.com/@Odhiya/" target="_blank">
                <img src="${FRONT_URL}/images/ytbIcon.png" alt="YouTube" title="YouTube" width="25" style="border: 0; height: auto;">
            </a>
        </div>
        <div class="footer-content">
            <strong>${getTranslation('whereToFindUs', lang)}</strong>
            <p>
                ${getTranslation('contactUs', lang)}
                <a href="mailto:${contactUsEmail}" style="color: #4CAF50; text-decoration: none;">${contactUsEmail}</a>
            </p>
            <p>
                <br>${getTranslation('odhiaAddress', lang)}
                <br>${phoneNumber}
            </p>
        </div>
        <div class="footer-content">
            <p>${getTranslation('rightsReserved', lang)}</p>
        </div>
    </div>
    </div>
</body>
</html>
    `;
    return summaryHTML;
}

exports.getReviewNotificationTemplate = (reviewData) => {
    const lang = reviewData.reviewedUser.favoriteLang;
    const direction = lang === 'ar' ? 'rtl' : 'ltr';

    return `
<!DOCTYPE html>
<html lang="${lang}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${getTranslation('newReviewNotification', lang)}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: ${direction};
            margin: 0;
            padding: 0;
        }

        .container {
            width: 100%;
            max-width: 750px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }

        h1 {
            color: #4CAF50;
            text-align: center;
        }

        p {
            margin: 0 0 10px;
            text-align: ${lang === 'ar' ? 'right' : 'left'};
        }

        .review-box {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }

        .rating {
            color: #FFD700;
            font-size: 24px;
        }

        .logo {
            text-align: center;
            margin-bottom: 20px;
        }

        .logo img {
            max-width: 150px;
            height: auto;
        }

        .footer {
            text-align: center;
            color: #777;
            font-size: 12px;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        .footer-content {
            font-size: 12px;
            line-height: 150%;
            color: #777777;
            margin-top: 20px;
            text-align: center !important;
        }

        .footer-content a {
            color: #4CAF50;
            text-decoration: none;
        }
        .footer-content p {
            text-align: center !important;
        }

        .footer-content a:hover {
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="logo">
            <a href="${FRONT_URL}" style="outline:none" tabindex="-1" target="_blank">
                <img alt="odhiya" src="${FRONT_URL}/images/favicon.png" title="odhiya" />
            </a>
        </div>
        <h1>${getTranslation('newReviewNotification', lang)}</h1>
        <p>${getTranslation('dearVendor', lang) + " " + reviewData.reviewedUser.firstName} ,</p >
        <p>${getTranslation('newReviewMessage', lang).replace('{customerName}', reviewData.reviewer.firstName + ' ' + reviewData.reviewer.lastName)}.</p>

        <div class="review-box">
            <p><strong>${getTranslation('orderID', lang)}:</strong> ${reviewData.subOrder}</p>
            <p><strong>${getTranslation('rating', lang)}:</strong> <span class="rating">${'★'.repeat(reviewData.rating)}${'☆'.repeat(5 - reviewData.rating)}</span></p>
            <p><strong>${getTranslation('ratingMessage', lang)}:</strong> ${reviewData.message}</p>
        </div>

        <p>${getTranslation('checkReviewDashboard', lang)}</p>
        <p><a href="${FRONT_URL}/${lang}/profile">${getTranslation('goToReviews', lang)}</a></p>
        <p>${getTranslation('bestRegards', lang)},</p>
        <p>${getTranslation('odhiaTeam', lang)}</p>

        <div class="footer">
            <strong>${getTranslation('socialMedia', lang)}</strong>
            <div class="social-icons">
                <a href="https://www.facebook.com/Odhiya.1/" target="_blank" style="margin: 0 5px;">
                    <img alt="Facebook" src="${FRONT_URL}/images/facebookIcon.png" title="Facebook" width="25" style="border: 0; height: auto;">
                </a>
                <a href="https://www.instagram.com/odhiya.1/" target="_blank" style="margin: 0 5px;">
                    <img alt="Instagram" src="${FRONT_URL}/images/instagramIcon.png" title="Instagram" width="25" style="border: 0; height: auto;">
                </a>
                <a href="https://www.tiktok.com/@odhiya1/" target="_blank" style="margin: 0 5px;">
                    <img alt="tiktok" src="${FRONT_URL}/images/tikTokIcon.png" title="tiktok" width="25" style="border: 0; height: auto;">
                </a>
                <a href="https://www.youtube.com/@Odhiya/" target="_blank" style="margin: 0 5px;">
                    <img alt="Youtube" src="${FRONT_URL}/images/ytbIcon.png" title="Youtube" width="25" style="border: 0; height: auto;">
                </a>
            </div>
            <div class="footer-content">
                <strong>${getTranslation('whereToFindUs', lang)}</strong>
                <p>${getTranslation('contactEmail', lang)} <a href="mailto:${contactUsEmail}" style="color: #4CAF50; text-decoration: none;">${contactUsEmail}</a></p>
                <p>${getTranslation('odhiaAddress', lang)}</p>
                <p>${getTranslation('rightsReserved', lang)}</p>
            </div>
        </div>
    </div >
</body >

</html >
    `;
};

exports.getLastChanceToReviewTemplate = (subOrderData, isVendor, favoriteLang) => {
    const lang = isVendor ? subOrderData.vendor.favoriteLang : subOrderData.costumer.favoriteLang;
    const direction = lang === 'ar' ? 'rtl' : 'ltr';
    // const isVendor = subOrderData.reviewedUser.role === 'vendor'; // Assuming role defines customer/vendor

    return `
<!DOCTYPE html>
<html lang="${lang}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${getTranslation('lastChanceToReview', lang)}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: ${direction};
            margin: 0;
            padding: 0;
            background-color: #f0f0f0;
        }

        .container {
            width: 100%;
            max-width: 750px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #ffffff;
        }

        h1 {
            color: #e47500;
            text-align: center;
            font-size: 28px;
        }

        p {
            margin: 0 0 10px;
            text-align: ${lang === 'ar' ? 'right' : 'left'};
        }

        .reminder-box {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }

        .alert-text {
            color: #e47500;
            font-weight: bold;
            text-align: center;
        }

        .logo {
            text-align: center;
            margin-bottom: 20px;
        }

        .logo img {
            max-width: 150px;
            height: auto;
        }

        .cta-button {
            display: block;
            width: fit-content;
            margin: 20px auto;
            padding: 12px 25px;
            background-color: #54b175;
            color: white !important;
            text-decoration: none;
            font-size: 16px;
            border-radius: 5px;
        }

        .cta-button:hover {
            background-color: #e47500;
        }

        .social-icons {
            margin: 20px 0;
        }

        .social-icons img {
            margin: 0 5px;
            width: 25px;
            height: auto;
        }

        .footer {
            text-align: center;
            color: #777;
            font-size: 12px;
            margin-top: 20px;
            margin-bottom: 10px;
        }

        .footer-content {
            font-size: 12px;
            line-height: 150%;
            color: #777777;
            margin-top: 20px;
            text-align: center !important;
        }

        .footer-content a {
            color: #4CAF50;
            text-decoration: none;
        }
        .footer-content p {
            text-align: center !important;
        }

        .footer-content a:hover {
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="logo">
            <a href="${FRONT_URL}" style="outline:none" tabindex="-1" target="_blank">
                <img alt="odhiya" src="${FRONT_URL}/images/favicon.png" title="odhiya" />
            </a>
        </div>

        <h1>${getTranslation('lastChanceToReview', lang)}</h1>

        <p>${getTranslation('dearUser', lang)},</p>
        <p>${isVendor ? getTranslation('reminderForVendor', lang) : getTranslation('reminderForCustomer', lang)}.</p>

        <div class="reminder-box">
            <p class="alert-text">${getTranslation('thisIsYourFinalChance', lang)}</p>
            <p>${getTranslation('orderID', lang)}: <strong>${subOrderData._id}</strong></p>
            <p>${getTranslation('youHave2Days', lang)}</p>
        </div>

        <p>${isVendor
            ? getTranslation('vendorReviewReminder', lang).replace('{customerName}', subOrderData.costumer.firstName + ' ' + subOrderData.costumer.lastName)
            : getTranslation('customerReviewReminder', lang).replace('{vendorName}', subOrderData.vendor.firstName + ' ' + subOrderData.vendor.lastName)}</p>

        <a href="${FRONT_URL}/${lang + (isVendor ? "/dashboard/received-orders?item=" + subOrderData._id : "/dashboard/my-orders?item=" + subOrderData.parent_id)}" class="cta-button">${getTranslation('submitReviewNow', lang)}</a>

        <p>${getTranslation('bestRegards', lang)},</p>
        <p>${getTranslation('odhiaTeam', lang)}</p>

        <div class="footer">
            <strong>${getTranslation('socialMedia', lang)}</strong>
            <div class="social-icons">
                <a href="https://www.facebook.com/Odhiya.1/" target="_blank">
                    <img alt="Facebook" src="${FRONT_URL}/images/facebookIcon.png" title="Facebook" />
                </a>
                <a href="https://www.instagram.com/odhiya.1/" target="_blank">
                    <img alt="Instagram" src="${FRONT_URL}/images/instagramIcon.png" title="Instagram" />
                </a>
                <a href="https://www.tiktok.com/@odhiya1/" target="_blank">
                    <img alt="tiktok" src="${FRONT_URL}/images/tikTokIcon.png" title="tiktok" />
                </a>
                <a href="https://www.youtube.com/@Odhiya/" target="_blank">
                    <img alt="Youtube" src="${FRONT_URL}/images/ytbIcon.png" title="Youtube" />
                </a>
            </div>
            <div class="footer-content">
                <strong>${getTranslation('whereToFindUs', lang)}</strong>
                <p>${getTranslation('contactEmail', lang)} <a href="mailto:${contactUsEmail}" style="color: #4CAF50; text-decoration: none;">${contactUsEmail}</a></p>
                <p>${getTranslation('odhiaAddress', lang)}</p>
                <p>${getTranslation('rightsReserved', lang)}</p>
            </div>
        </div>
    </div >
</body >

</html >
    `;
};


exports.getTicketResolvedTemplate = (ticket, lang) => {
    let resolvedHTML = `<!DOCTYPE html>
<html lang="${lang}" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:v="urn:schemas-microsoft-com:vml">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${getTranslation('ticketResolved', lang)}</title>
    <style>
        body {
            background-color: #f7f7f7;
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            direction: ${lang === 'ar' ? 'rtl' : 'ltr'};
            text-align: ${lang === 'ar' ? 'right' : 'left'};
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #fff;
            border: 1px solid #e0e0e0;
            padding: 20px;
        }
        .header img {
            max-width: 150px;
            display: block;
            margin: 0 auto 20px;
        }
        h1 {
            color: #05144b;
            font-size: 20px;
            text-align: center;
        }
        .content p {
            margin: 10px 0;
        }
        .resolution {
            background-color: #f9f9f9;
            padding: 15px;
            margin: 15px 0;
            border-left: 3px solid #05144b;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table th, table td {
            padding: 10px;
            border: 1px solid #e0e0e0;
            text-align: left;
        }
        table th {
            background-color: #f2f2f2;
        }
        .footer {
            font-size: 12px;
            color: #666;
            text-align: center;
            margin-top: 20px;
        }
        .social a img {
            width: 20px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="${FRONT_URL}/${lang}" target="_blank">
                <img src="${FRONT_URL}/images/home/<USER>" alt="ZTechEngineering">
            </a>
        </div>
        <div class="content">
            <h1>${getTranslation('ticketResolved', lang)}</h1>
            <p>${getTranslation('dear', lang)} ${ticket.creator.firstName} ${ticket.creator.lastName},</p>
            <p>${getTranslation('yourTicketHasBeenResolved', lang)} <strong>"${ticket.subject}"</strong>.</p>
            <div class="resolution">
                <strong>${getTranslation('resolutionComment', lang)}:</strong><br>
                ${ticket.resolutionComment}
            </div>
            <table>
                <tr><th>${getTranslation('ticketId', lang)}</th><td>#${ticket.identifiant}</td></tr>
                <tr><th>${getTranslation('subject', lang)}</th><td>${ticket.subject}</td></tr>
                <tr><th>${getTranslation('createdAt', lang)}</th><td>${new Date(ticket.createdAt).toLocaleDateString(lang)}</td></tr>
                <tr><th>${getTranslation('status', lang)}</th><td>${getTranslation('resolved', lang)}</td></tr>
            </table>
            <p>${getTranslation('bestRegards', lang)},<br>ZTechEngineering</p>
        </div>
        <div class="footer">
            <div class="social">
                ${Object.values(SOCIAL_MEDIA_LINKS).map(link => `<a href="${link.url}" target="_blank">
                    <img src="${FRONT_URL}${link.icon}" alt="${link.title}">
                </a>`).join('')}
            </div>
            <p>${getTranslation('contactEmail', lang)}: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>${getTranslation('ZTechEngineeringAddress', lang)}</p>
            <p>${getTranslation('rightsReserved', lang)}</p>
        </div>
    </div>
</body>
</html>

`;
    return resolvedHTML;
};


exports.getTicketCreatedTemplate = (ticket, lang) => {
    let createdHTML = `<!DOCTYPE html>
<html lang="${lang}" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:v="urn:schemas-microsoft-com:vml">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${getTranslation('ticketCreated', lang)}</title>
    <style>
        body {
            background-color: #f7f7f7;
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            direction: ${lang === 'ar' ? 'rtl' : 'ltr'};
            text-align: ${lang === 'ar' ? 'right' : 'left'};
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #fff;
            border: 1px solid #e0e0e0;
            padding: 20px;
        }
        .header img {
            max-width: 150px;
            display: block;
            margin: 0 auto 20px;
        }
        h1 {
            color: #05144b;
            font-size: 20px;
            text-align: center;
        }
        .content p {
            margin: 10px 0;
        }
        .details {
            background-color: #f9f9f9;
            padding: 15px;
            margin: 15px 0;
            border-left: 3px solid #05144b;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table th, table td {
            padding: 10px;
            border: 1px solid #e0e0e0;
            text-align: left;
        }
        table th {
            background-color: #f2f2f2;
        }
        .footer {
            font-size: 12px;
            color: #666;
            text-align: center;
            margin-top: 20px;
        }
        .social a img {
            width: 20px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="${FRONT_URL}/${lang}" target="_blank">
                <img src="${FRONT_URL}/images/home/<USER>" alt="ZTechEngineering">
            </a>
        </div>
        <div class="content">
            <h1>${getTranslation('ticketCreated', lang)}</h1>
            <p>${getTranslation('dearAdmin', lang)},</p>
            <p>${getTranslation('newTicketCreated', lang)} <strong>"${ticket.subject}"</strong>.</p>
            <div class="details">
                <strong>${getTranslation('creator', lang)}:</strong> ${ticket.creator.firstName} ${ticket.creator.lastName}<br>
                <strong>Email:</strong> ${ticket.creator.email}<br>
                <strong>${getTranslation('ticketId', lang)}:</strong> ${ticket.identifiant}<br>
                <strong>${getTranslation('message', lang)}:</strong> ${ticket.message}<br>
            </div>
            <table>
                <tr><th>${getTranslation('ticketId', lang)}</th><td>${ticket.identifiant}</td></tr>
                <tr><th>${getTranslation('subject', lang)}</th><td>${ticket.subject}</td></tr>
                <tr><th>${getTranslation('createdAt', lang)}</th><td>${new Date(ticket.createdAt).toLocaleDateString(lang)}</td></tr>
                <tr><th>${getTranslation('priority', lang)}</th><td>${ticket.priority}</td></tr>
                <tr><th>${getTranslation('service', lang)}</th><td>${ticket.service}</td></tr>
            </table>
            <p>${getTranslation('bestRegards', lang)},<br>ZTechEngineering</p>
        </div>
        <div class="footer">
            <div class="social">
                ${Object.values(SOCIAL_MEDIA_LINKS).map(link => `<a href="${link.url}" target="_blank">
                    <img src="${FRONT_URL}${link.icon}" alt="${link.title}">
                </a>`).join('')}
            </div>
            <p>${getTranslation('contactEmail', lang)}: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>${getTranslation('ZTechEngineeringAddress', lang)}</p>
            <p>${getTranslation('rightsReserved', lang)}</p>
        </div>
    </div>
</body>
</html>

`;
    return createdHTML;
};


exports.getSubOrderEmailTemplate = (subOrder, user, lang) => {
    let subOrderHTML = `<!DOCTYPE html>
  <html lang="${lang}" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:v="urn:schemas-microsoft-com:vml">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
          body {
              background-color: #f7f7f7;
              margin: 0;
              padding: 0;
              font-family: Arial, sans-serif;
              direction: ${lang === 'ar' ? 'rtl' : 'ltr'};
              text-align: ${lang === 'ar' ? 'right' : 'left'};
              color: #333;
              line-height: 1.6;
          }
          .container {
              max-width: 600px;
              margin: 20px auto;
              background-color: #fff;
              border: 1px solid #e0e0e0;
              padding: 20px;
          }
          .header img {
              max-width: 150px;
              display: block;
              margin: 0 auto 20px;
          }
          h1 {
              color: #05144b;
              font-size: 20px;
              text-align: center;
          }
          .content p {
              margin: 10px 0;
          }
          .details {
              background-color: #f9f9f9;
              padding: 15px;
              margin: 15px 0;
              border-left: 3px solid #05144b;
          }
          table {
              width: 100%;
              border-collapse: collapse;
              margin: 20px 0;
          }
          table th, table td {
              padding: 10px;
              border: 1px solid #e0e0e0;
              text-align: left;
          }
          table th {
              background-color: #f2f2f2;
          }
          .footer {
              font-size: 12px;
              color: #666;
              text-align: center;
              margin-top: 20px;
          }
          .social a img {
              width: 20px;
              margin: 5px;
          }
      </style>
  </head>
  <body>
      <div class="container">
          <div class="header">
              <a href="${FRONT_URL}/${lang}" target="_blank">
                  <img src="${FRONT_URL}/images/home/<USER>" alt="ZTechEngineering">
              </a>
          </div>
          <div class="content">
              <h1>${getTranslation('subOrderStatusUpdate', lang)}</h1>
              <p>${getTranslation('dearCustomer', lang)}, ${user.lastName}</p>
              <p>${getTranslation('yourSubOrderStatusUpdated', lang)} <strong>"${getTranslation(subOrder.status, lang)}"</strong>.</p>
              <div class="details">
                  <strong>${getTranslation('subOrderId', lang)}:</strong> ${subOrder.identifiant}<br>
                  <strong>${getTranslation('packageName', lang)}:</strong> ${subOrder.package.name}<br>
                  <strong>${getTranslation('quantity', lang)}:</strong> ${subOrder.quantity}<br>
                  <strong>${getTranslation('price', lang)}:</strong> ${subOrder.price} MAD<br>
                  <strong>${getTranslation('issuedOn', lang)}:</strong> ${new Date(subOrder.createdAt).toLocaleDateString(lang)}<br>
                  <strong>${getTranslation('status', lang)}:</strong> ${getTranslation(subOrder.status, lang)}<br>
              </div>
              <table>
                  <tr><th>${getTranslation('subOrderId', lang)}</th><td>${subOrder.identifiant}</td></tr>
                  <tr><th>${getTranslation('packageName', lang)}</th><td>${subOrder.package.name}</td></tr>
                  <tr><th>${getTranslation('quantity', lang)}</th><td>${subOrder.quantity}</td></tr>
                  <tr><th>${getTranslation('price', lang)}</th><td>${subOrder.price} MAD</td></tr>
                  <tr><th>${getTranslation('issuedOn', lang)}</th><td>${new Date(subOrder.createdAt).toLocaleDateString(lang)}</td></tr>
                  <tr><th>${getTranslation('status', lang)}</th><td>${subOrder.status}</td></tr>
              </table>
              <p>${getTranslation('bestRegards', lang)},<br>ZTechEngineering</p>
          </div>
          <div class="footer">
              <div class="social">
                  ${Object.values(SOCIAL_MEDIA_LINKS).map(link => `<a href="${link.url}" target="_blank">
                      <img src="${FRONT_URL}${link.icon}" alt="${link.title}">
                  </a>`).join('')}
              </div>
              <p>${getTranslation('contactEmail', lang)}: <a href="mailto:<EMAIL>"><EMAIL></a></p>
              <p>${getTranslation('ZTechEngineeringAddress', lang)}</p>
              <p>${getTranslation('rightsReserved', lang)}</p>
          </div>
      </div>
  </body>
  </html>
  `;
    return subOrderHTML;
  };

// Template for SSL certificate status update emails
exports.getSSLCertificateStatusUpdateTemplate = (certificate, subOrder, user, lang) => {
  const formatDate = (date) => {
    if (!date) return '-';
    return new Date(date).toLocaleDateString(lang);
  };

  // Get status in a user-friendly format
  const getStatusText = (status) => {
    // Access nested translations manually since getTranslation doesn't support dot notation
    try {
      const filePath = path.join(
        __dirname,
        "../..",
        "locales",
        lang.split("-")[0] || "en",
        "translation.json"
      );
      const translations = JSON.parse(fs.readFileSync(filePath, "utf8"));

      if (translations.ssl) {
        switch(status) {
          case 'ISSUED': return translations.ssl.status_issued || 'Issued';
          case 'INSTALLED': return translations.ssl.status_installed || 'Installed';
          case 'PROCESSING': return translations.ssl.status_processing || 'Processing';
          case 'PENDING': return translations.ssl.status_pending || 'Pending';
          case 'EXPIRED': return translations.ssl.status_expired || 'Expired';
          case 'REVOKED': return translations.ssl.status_revoked || 'Revoked';
          default: return status;
        }
      }
    } catch (error) {
      console.error('Error accessing SSL status translations:', error);
    }

    // Fallback to hardcoded English values if translations fail
    switch(status) {
      case 'ISSUED': return 'Issued';
      case 'INSTALLED': return 'Installed';
      case 'PROCESSING': return 'Processing';
      case 'PENDING': return 'Pending';
      case 'EXPIRED': return 'Expired';
      case 'REVOKED': return 'Revoked';
      default: return status;
    }
  };

  let summaryHTML = '';
  summaryHTML += `
<!DOCTYPE html>
<html lang="${lang}" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:v="urn:schemas-microsoft-com:vml">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${getTranslation('ssl.certificate_status_update', lang)}</title>
</head>
<body style="background-color: #f4f4f4; margin: 0; padding: 0; direction: ${lang === 'ar' ? 'rtl' : 'ltr'}; text-align: ${lang === 'ar' ? 'right' : 'left'};">
    <div style="width: 100% !important; max-width: 800px; margin: 0 auto; background-color: #ffffff; font-family: 'Montserrat', sans-serif; padding: 20px;">
        <div style="background-color: #ffffff; text-align: center; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);">
            <div style="text-align: center; max-width: 150px; margin: 0 auto;">
                <a href="${FRONT_URL}/${lang}" style="outline:none" tabindex="-1" target="_blank">
                    <img alt="ztechengineering" height="auto" src="${FRONT_URL}/images/home/<USER>" style="display: block; height: auto; border: 0; width: 100%;" title="ztechengineering" width="180" />
                </a>
            </div>
            <h1 style="color: #05144b; font-size: 30px; margin: 10px 0;">SSL Certificate Status Update</h1>
            <p style="color: #05144b; font-size: 16px; line-height: 1.6; margin: 10px 20px;">Your SSL certificate status has been updated to <strong>${getStatusText(certificate.status)}</strong></p>
        </div>
        <div style="font-size: 14px; direction: ${lang === 'ar' ? 'rtl' : 'ltr'}; text-align: ${lang === 'ar' ? 'right' : 'left'}; margin-top: 20px;">
            <p>${getTranslation('dear', lang)} ${user.firstName} ${user.lastName},</p>
            <p>If you have any questions about your SSL certificate, please contact our support team.</p>
            <p>${getTranslation('bestRegards', lang)},</p>
            <p>ZTechEngineering</p>
        </div>
        <div style="background-color: #ffffff; padding: 20px; margin: 10px 0; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);">
            <div style="display: block; text-align: ${lang === 'ar' ? 'right' : 'left'}; direction: ${lang === 'ar' ? 'rtl' : 'ltr'};">
                <h2 style="font-size: 18px; margin-bottom: 10px; color: #333333;">SSL Certificate Status Update</h2>
                <br/>
                <p style="direction: ${lang === 'ar' ? 'rtl' : 'ltr'}; font-size: 14px;"><strong>${getTranslation('subOrderId', lang)}:</strong> ${subOrder._id}</p>
            </div>

            <div style="background-color: #f2f2f2; color: #333333; padding: 10px; margin-bottom: 10px; border-radius: 10px; overflow: hidden;">
                <div style="background-color: #ffffff; color: #333333; margin-bottom: 10px; border-radius: 10px; overflow: hidden; display: flex; align-items: center; justify-content:space-between; width: 100%;">
                    <div style="padding: 10px; flex: 1; font-size: 14px; direction: ${lang === 'ar' ? 'rtl' : 'ltr'};">
                        <div><strong>Domain:</strong> ${certificate.domain || '-'}</div>
                        <div><strong>Validation Email:</strong> ${certificate.validationEmail || '-'}</div>
                        <div><strong>Issued At:</strong> ${formatDate(certificate.issuedAt)}</div>
                        <div><strong>Expires At:</strong> ${formatDate(certificate.expiresAt)}</div>
                        <div><strong>Status:</strong> ${getStatusText(certificate.status)}</div>
                    </div>
                </div>
            </div>
            <table style="border: 1px solid #000000; border-collapse: collapse; width: 100%; max-width: 600px; margin: 0 auto; margin-top: 20px; direction: ${lang === 'ar' ? 'rtl' : 'ltr'};">
                <thead>
                    <tr>
                        <th style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px; background-color: #dddddd;">${getTranslation('summary', lang)}</th>
                        <th style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px; background-color: #dddddd;">${getTranslation('details', lang)}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>${getTranslation('packageName', lang)}:</strong></td>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>${subOrder.package.name}</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>Domain:</strong></td>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>${certificate.domain || '-'}</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>Status:</strong></td>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: ${lang === 'ar' ? 'right' : 'left'}; font-size: 14px;"><strong>${getStatusText(certificate.status)}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div style="text-align: center; padding: 20px; background: #f9f9f9; border-top: 1px solid #eeeeee; margin-top: 20px;">
            <strong>${getTranslation('socialMedia', lang)}</strong>
            <div style="padding: 10px 0;">
                ${Object.values(SOCIAL_MEDIA_LINKS)
            .map(
                (link) => `
                            <a href="${link.url}" target="_blank" style="margin: 0 5px;">
                                <img alt="${link.title}" src="${FRONT_URL}${link.icon}" title="${link.title}" width="25" style="vertical-align: middle;">
                            </a>`
            )
            .join('')}
            </div>
            <div style="padding-top: 10px; text-align: center;">
                <strong>${getTranslation('whereToFindUs', lang)}</strong>
                <p>${getTranslation('contactEmail', lang)} <a href="mailto:<EMAIL>" style="color: #497ef7; text-decoration: none;"><EMAIL></a></p>
                <p>${getTranslation('ZTechEngineeringAddress', lang)}</p>
                <p>${getTranslation('rightsReserved', lang)}</p>
            </div>
        </div>
    </div>
</body>
</html>
`;
  return summaryHTML;
};

// Template for SSL certificate activation admin notification
exports.getSSLCertificateActivationAdminTemplate = (certificate, subOrder, user) => {
  // Always use English for admin notifications
  const lang = "en";

  let summaryHTML = '';
  summaryHTML += `
<!DOCTYPE html>
<html lang="${lang}" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:v="urn:schemas-microsoft-com:vml">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New SSL Certificate Activation</title>
</head>
<body style="background-color: #f4f4f4; margin: 0; padding: 0; direction: ltr; text-align: left;">
    <div style="width: 100% !important; max-width: 800px; margin: 0 auto; background-color: #ffffff; font-family: 'Montserrat', sans-serif; padding: 20px;">
        <div style="background-color: #ffffff; text-align: center; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);">
            <div style="text-align: center; max-width: 150px; margin: 0 auto;">
                <a href="${FRONT_URL}/${lang}" style="outline:none" tabindex="-1" target="_blank">
                    <img alt="ztechengineering" height="auto" src="${FRONT_URL}/images/home/<USER>" style="display: block; height: auto; border: 0; width: 100%;" title="ztechengineering" width="180" />
                </a>
            </div>
            <h1 style="color: #05144b; font-size: 30px; margin: 10px 0;">New SSL Certificate Activation</h1>
            <p style="color: #05144b; font-size: 16px; line-height: 1.6; margin: 10px 20px;">A customer has activated an SSL certificate that requires your attention.</p>
        </div>
        <div style="font-size: 14px; direction: ltr; text-align: left; margin-top: 20px;">
            <p>Dear Admin,</p>
            <p>A new SSL certificate has been activated by a customer and is now in <strong>PROCESSING</strong> status. Please review and process this certificate.</p>
            <p><strong>Customer:</strong> ${user.firstName} ${user.lastName} (${user.email})</p>
            <p>Best regards,</p>
            <p>ZTechEngineering System</p>
        </div>
        <div style="background-color: #ffffff; padding: 20px; margin: 10px 0; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);">
            <div style="display: block; text-align: left; direction: ltr;">
                <h2 style="font-size: 18px; margin-bottom: 10px; color: #333333;">Certificate Details</h2>
                <br/>
                <p style="direction: ltr; font-size: 14px;"><strong>SubOrder ID:</strong> ${subOrder._id}</p>
            </div>

            <div style="background-color: #f2f2f2; color: #333333; padding: 10px; margin-bottom: 10px; border-radius: 10px; overflow: hidden;">
                <div style="background-color: #ffffff; color: #333333; margin-bottom: 10px; border-radius: 10px; overflow: hidden; display: flex; align-items: center; justify-content:space-between; width: 100%;">
                    <div style="padding: 10px; flex: 1; font-size: 14px; direction: ltr;">
                        <div><strong>Domain:</strong> ${certificate.domain || '-'}</div>
                        <div><strong>Validation Email:</strong> ${certificate.validationEmail || '-'}</div>
                        <div><strong>Installation Service:</strong> ${certificate.installService ? 'Yes' : 'No'}</div>
                        <div><strong>Status:</strong> PROCESSING</div>
                    </div>
                </div>
            </div>
            <table style="border: 1px solid #000000; border-collapse: collapse; width: 100%; max-width: 600px; margin: 0 auto; margin-top: 20px; direction: ltr;">
                <thead>
                    <tr>
                        <th style="border: 1px solid #000000; padding: 10px; text-align: left; font-size: 14px; background-color: #dddddd;">Summary</th>
                        <th style="border: 1px solid #000000; padding: 10px; text-align: left; font-size: 14px; background-color: #dddddd;">Details</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: left; font-size: 14px;"><strong>Package Name:</strong></td>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: left; font-size: 14px;"><strong>${subOrder.package.name}</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: left; font-size: 14px;"><strong>Domain:</strong></td>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: left; font-size: 14px;"><strong>${certificate.domain || '-'}</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: left; font-size: 14px;"><strong>Installation Service:</strong></td>
                        <td style="border: 1px solid #000000; padding: 10px; text-align: left; font-size: 14px;"><strong>${certificate.installService ? 'Yes' : 'No'}</strong></td>
                    </tr>
                </tbody>
            </table>

            <div style="margin-top: 20px; text-align: center;">
                <a href="${FRONT_URL}/admin/orders/${subOrder._id}" style="display: inline-block; padding: 10px 20px; background-color: #05144b; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;">
                    View in Admin Panel
                </a>
            </div>
        </div>
        <div style="text-align: center; padding: 20px; background: #f9f9f9; border-top: 1px solid #eeeeee; margin-top: 20px;">
            <strong>Follow Us</strong>
            <div style="padding: 10px 0;">
                ${Object.values(SOCIAL_MEDIA_LINKS)
            .map(
                (link) => `
                            <a href="${link.url}" target="_blank" style="margin: 0 5px;">
                                <img alt="${link.title}" src="${FRONT_URL}${link.icon}" title="${link.title}" width="25" style="vertical-align: middle;">
                            </a>`
            )
            .join('')}
            </div>
            <div style="padding-top: 10px; text-align: center;">
                <strong>Where to Find Us</strong>
                <p>Contact Email: <a href="mailto:<EMAIL>" style="color: #497ef7; text-decoration: none;"><EMAIL></a></p>
                <p>ZTechEngineering Address</p>
                <p>All rights reserved</p>
            </div>
        </div>
    </div>
</body>
</html>
`;
  return summaryHTML;
};