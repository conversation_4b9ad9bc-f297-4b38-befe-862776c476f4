[{"C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\api\\contact\\route.js": "1", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\AccountRole.js": "2", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\AccountState.js": "3", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\BanksList.js": "4", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\constant.js": "5", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\ConstStatus.js": "6", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\InquiryState.js": "7", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\MarocRegions.js": "8", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\NotificationNavCode.js": "9", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\PaymentMethodConfig.js": "10", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\ProductStatus.js": "11", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\context\\AuthContext.jsx": "12", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\context\\GlobalStateContext.jsx": "13", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\helpers\\helpers.js": "14", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\hook\\useCountdown.jsx": "15", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\hook\\useDeviceScale.jsx": "16", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\hook\\useIsMobile.jsx": "17", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\layout.jsx": "18", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\lib\\apiService.js": "19", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\lib\\axiosInstance.js": "20", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\lib\\notion.js": "21", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\loading.jsx": "22", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\not-found.js": "23", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\adminService.js": "24", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\authService.js": "25", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\brandService.js": "26", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\cartService.js": "27", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\categoryService.js": "28", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\orderService.js": "29", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\packageService.js": "30", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\paymentService.js": "31", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\profileService.js": "32", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\specificationService.js": "33", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\ticketService.js": "34", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\utils\\dateFormatter.js": "35", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\utils\\errorHandler.js": "36", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\utils\\helperFunctions.js": "37", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\about-us\\page.jsx": "38", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\cart\\page.jsx": "39", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\cart\\payment\\page.jsx": "40", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\data-deletion\\page.jsx": "41", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\domains\\page.jsx": "42", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\hosting-plans\\page.jsx": "43", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\layout.jsx": "44", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\orders\\page.jsx": "45", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\payment-history\\page.jsx": "46", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\payments\\[paymentId]\\page.jsx": "47", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\profile\\edit\\page.jsx": "48", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\profile\\page.jsx": "49", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\sidebar.jsx": "50", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\ssl-certificates\\page.jsx": "51", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\support\\add-ticket\\page.jsx": "52", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\support\\announcements\\page.jsx": "53", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\support\\layout.jsx": "54", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\support\\sidebar.jsx": "55", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\support\\tickets\\page.jsx": "56", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\support\\tickets\\[ticketId]\\page.jsx": "57", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\layout.jsx": "58", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\page.jsx": "59", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\about-us\\banner.jsx": "60", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\about-us\\ourMission.jsx": "61", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\about-us\\whyChooseUs.jsx": "62", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\AdminFilters.jsx": "63", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\AdminTable.jsx": "64", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\ConfirmationModal.jsx": "65", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\package\\packageFilters.jsx": "66", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\package\\PreviewPackage.jsx": "67", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\SearchHeader.jsx": "68", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\SearchInput.jsx": "69", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\UserModal.jsx": "70", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ai-services\\aiIntro.jsx": "71", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ai-services\\aiMain.jsx": "72", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ai-services\\whatWeOffers.jsx": "73", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ai-services\\whatWeOffers2.jsx": "74", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\auth\\avatarWithUserDropdown.jsx": "75", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\auth\\emailVerification.jsx": "76", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\auth\\googleLoginButton.jsx": "77", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\auth\\login.jsx": "78", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\auth\\register.jsx": "79", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\auth\\resetPassword.jsx": "80", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\auth\\sharedBetweenAuth.js": "81", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\auth\\socialLogin.jsx": "82", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\avatar\\AvatarIcon.jsx": "83", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\blog\\blogPostContent.jsx": "84", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\blog\\postCard.jsx": "85", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\blog\\recentPosts.jsx": "86", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\blog\\search.jsx": "87", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cart\\billingInfoForm.jsx": "88", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cart\\cartItem.jsx": "89", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cart\\cartItemsList.jsx": "90", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cart\\summary.jsx": "91", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cloud-maroc\\cloudMarocIntro.jsx": "92", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cloud-maroc\\cloudVps.jsx": "93", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cloud-maroc\\sharedCloud.jsx": "94", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cloud-security\\cloudSecurityIntro.jsx": "95", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cloud-security\\cloudSecurityMain.jsx": "96", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cloud-security\\offersSwiper.jsx": "97", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cloud-security\\offersSwiper2.jsx": "98", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\guide\\banner.jsx": "99", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\guide\\hostingAdvantages.jsx": "100", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\guide\\ourSolutionsGuide.jsx": "101", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\guide\\whyChooseUs.jsx": "102", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\aboutUs.jsx": "103", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\banner.jsx": "104", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\chatWithUs.jsx": "105", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\cloudAdvantages.jsx": "106", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\cloudAdvantagesDesktop.jsx": "107", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\cloudAdvantagesMobile.jsx": "108", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\cloudMoroc.jsx": "109", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\header.jsx": "110", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\mainNavbar.jsx": "111", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\mobileCarousel.jsx": "112", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\otherServices.jsx": "113", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\services.jsx": "114", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\siteBranding.jsx": "115", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\testimonials.jsx": "116", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\topNavbar.jsx": "117", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\topNavbarcopy.jsx": "118", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\TypingAnimation.jsx": "119", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\whotrustUs.jsx": "120", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\whyChooseUs.jsx": "121", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\hosting\\hostingPricingTable.jsx": "122", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\hosting\\intro.jsx": "123", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-a\\banner.jsx": "124", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-a\\callToAction.jsx": "125", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-a\\PopularOffers.jsx": "126", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-a\\testimonials.jsx": "127", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-a\\testimonials2.jsx": "128", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-a\\whyTrustUs.jsx": "129", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-b\\banner.jsx": "130", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-b\\discountOffer.jsx": "131", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-b\\servicesPense.jsx": "132", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\local-switcher.jsx": "133", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\managed-services\\managedServicesIntro.jsx": "134", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\managed-services\\managedServicesMain.jsx": "135", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\order\\paymentStatusModal.jsx": "136", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\pdf\\InvoiceTemplate.jsx": "137", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\profile\\avatarUploader.jsx": "138", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\profile\\editEmailForm.jsx": "139", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\profile\\editPasswordForm.jsx": "140", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\profile\\editProfileForm.jsx": "141", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\profile\\inputOneTimePassword.jsx": "142", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\promotion\\promotionIntro.jsx": "143", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\servers\\serversIntro.jsx": "144", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\servers\\serversMain.jsx": "145", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\alertCustom.jsx": "146", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\contactForm2.jsx": "147", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\customButton.jsx": "148", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\customSwiper.jsx": "149", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\customSwiper2.jsx": "150", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\faq.jsx": "151", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\footer2.jsx": "152", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\landingTestimonials.jsx": "153", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\navDropDown.jsx": "154", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\notification.jsx": "155", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\whatsAppFloatingButton.jsx": "156", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ssl-old\\sslIntro.jsx": "157", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ssl-old\\sslMain.jsx": "158", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\web-dev\\webDevIntro.jsx": "159", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\web-dev\\webDevPricingTable.jsx": "160", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\icons\\svgIcons.js": "161", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\middleware.jsx": "162", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\404\\page.jsx": "163", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\ai-services\\page.jsx": "164", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\cloud-security\\page.jsx": "165", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\guide\\page.jsx": "166", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\managed-services\\page.jsx": "167", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\privacy-policy\\page.jsx": "168", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\ssl\\page.jsx": "169", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\terms-of-service\\page.jsx": "170", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\web-development\\page.jsx": "171", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\auth\\forgotPassword\\page.jsx": "172", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\auth\\login\\page.jsx": "173", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\auth\\register\\page.jsx": "174", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\auth\\reset-password\\page.jsx": "175", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\auth\\verification\\page.jsx": "176", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\blog\\page.jsx": "177", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\blog\\[slug]\\page.jsx": "178", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\hosting\\dedicated\\page.jsx": "179", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\hosting\\page.jsx": "180", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\hosting\\shared\\page.jsx": "181", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\hosting\\vps\\page.jsx": "182", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\hosting\\vps2\\page.jsx": "183", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\hosting\\pricingPlanGrid.jsx": "184", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\chatbotService.js": "185", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\Chatbot\\ChatbotButton.jsx": "186", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\Chatbot\\ChatbotContainer.jsx": "187", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\Chatbot\\ChatbotWindow.jsx": "188", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\Chatbot\\ChatMessage.jsx": "189", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\chatbot\\chatbot.jsx": "190", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\chatbot\\chatMessage.jsx": "191", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\FloatingButtonsContainer.jsx": "192", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\errorAlert.jsx": "193", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\ImageModal.jsx": "194", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\hosting\\pricingPlanGridWeb.jsx": "195", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\banner2.jsx": "196", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\homeBanner.jsx": "197", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\countdown.jsx": "198", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\web-development\\page.jsx": "199", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ssl\\SSLCard.jsx": "200", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ssl\\SSLFeatures.jsx": "201", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ssl\\SSLFilters.jsx": "202", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ssl\\SSLHero.jsx": "203", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\hosting\\hPackagesPlanGrid.jsx": "204", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\cart\\page.jsx": "205", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cart2\\billingInfoForm.jsx": "206", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cart2\\cartItem.jsx": "207", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cart2\\cartItemsList.jsx": "208", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cart2\\summary.jsx": "209", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\SecButton.jsx": "210", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\hook\\useDebounce.jsx": "211", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\sslService.js": "212", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\landing\\_demandez-devis\\page.jsx": "213", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\landing\\_demandez-devis-b\\page.jsx": "214", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\_promotion\\page.jsx": "215", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\_servers\\page.jsx": "216", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\ssl-certificates\\activate\\[id]\\page.jsx": "217", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\_page.jsx": "218", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\CTAButtons.jsx": "219", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\DomaineSearch.jsx": "220", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\Section.jsx": "221", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\managed-services\\ManagedServicesDetails.jsx": "222", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\managed-services\\ManagedServicesReasons.jsx": "223", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\profile\\companyInfoForm.jsx": "224", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\cloud-maroc\\page.jsx": "225", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\companyIntro.jsx": "226", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\DomainExtensionDropdown.jsx": "227", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\TestimonialsSection.jsx": "228", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\WhoTrustUs2.jsx": "229", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\faq2.jsx": "230", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\activity-logs\\page.jsx": "231", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\chats\\page.jsx": "232", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\chats\\[id]\\page.jsx": "233", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\jobs\\page.jsx": "234", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\layout.jsx": "235", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\login\\page.jsx": "236", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\marketing\\page.jsx": "237", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\orders\\page.jsx": "238", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\orders\\[orderId]\\page.jsx": "239", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\packages\\packageForm.jsx": "240", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\packages\\packagesTable.jsx": "241", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\packages\\page.jsx": "242", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\page.jsx": "243", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\pages\\page.jsx": "244", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\posts\\page.jsx": "245", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\settings\\page.jsx": "246", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\sidebar.jsx": "247", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\support\\page.jsx": "248", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\support\\[ticketId]\\page.jsx": "249", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\users\\page.jsx": "250", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\_page.jsx": "251", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\global-error.jsx": "252", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\hook\\useIsMobile.js": "253", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\utils\\logFormatter.js": "254", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\utils\\pendingCartHandler.js": "255", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\layout.js": "256", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\ChatbotContextModal.jsx": "257", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\avatar\\UserAvatar.jsx": "258", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\Chatbot\\AddToCartButton.jsx": "259", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\Chatbot\\ChatRecommendations.jsx": "260", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\Chatbot\\ConfirmDialog.jsx": "261", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\Notifications.jsx": "262", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\services\\adminService.js": "263", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\userService.js": "264", "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\user\\UserNotifications.jsx": "265"}, {"size": 6643, "mtime": 1746717131355, "results": "266", "hashOfConfig": "267"}, {"size": 104, "mtime": 1738576037252, "results": "268", "hashOfConfig": "267"}, {"size": 194, "mtime": 1741965017452, "results": "269", "hashOfConfig": "267"}, {"size": 1308, "mtime": 1738576037253, "results": "270", "hashOfConfig": "267"}, {"size": 621, "mtime": 1747915795801, "results": "271", "hashOfConfig": "267"}, {"size": 423, "mtime": 1747049840345, "results": "272", "hashOfConfig": "267"}, {"size": 695, "mtime": 1738576037254, "results": "273", "hashOfConfig": "267"}, {"size": 1208, "mtime": 1738576037255, "results": "274", "hashOfConfig": "267"}, {"size": 376, "mtime": 1738576037255, "results": "275", "hashOfConfig": "267"}, {"size": 127, "mtime": 1741965017453, "results": "276", "hashOfConfig": "267"}, {"size": 84, "mtime": 1741698155838, "results": "277", "hashOfConfig": "267"}, {"size": 4104, "mtime": 1747661378208, "results": "278", "hashOfConfig": "267"}, {"size": 573, "mtime": 1738576037257, "results": "279", "hashOfConfig": "267"}, {"size": 9927, "mtime": 1747049840350, "results": "280", "hashOfConfig": "267"}, {"size": 1431, "mtime": 1738576037259, "results": "281", "hashOfConfig": "267"}, {"size": 1206, "mtime": 1738576037259, "results": "282", "hashOfConfig": "267"}, {"size": 492, "mtime": 1739093207287, "results": "283", "hashOfConfig": "267"}, {"size": 4617, "mtime": 1747661378274, "results": "284", "hashOfConfig": "267"}, {"size": 412, "mtime": 1738576037261, "results": "285", "hashOfConfig": "267"}, {"size": 482, "mtime": 1742236744909, "results": "286", "hashOfConfig": "267"}, {"size": 5379, "mtime": 1738576037262, "results": "287", "hashOfConfig": "267"}, {"size": 266, "mtime": 1739893372051, "results": "288", "hashOfConfig": "267"}, {"size": 109, "mtime": 1738576037262, "results": "289", "hashOfConfig": "267"}, {"size": 7440, "mtime": 1747915795810, "results": "290", "hashOfConfig": "267"}, {"size": 869, "mtime": 1738576037263, "results": "291", "hashOfConfig": "267"}, {"size": 719, "mtime": 1741698155882, "results": "292", "hashOfConfig": "267"}, {"size": 907, "mtime": 1747915795811, "results": "293", "hashOfConfig": "267"}, {"size": 519, "mtime": 1738576037264, "results": "294", "hashOfConfig": "267"}, {"size": 1287, "mtime": 1747049840356, "results": "295", "hashOfConfig": "267"}, {"size": 313, "mtime": 1741698155884, "results": "296", "hashOfConfig": "267"}, {"size": 480, "mtime": 1741698155884, "results": "297", "hashOfConfig": "267"}, {"size": 1037, "mtime": 1747049840357, "results": "298", "hashOfConfig": "267"}, {"size": 598, "mtime": 1738576037265, "results": "299", "hashOfConfig": "267"}, {"size": 1110, "mtime": 1740752742609, "results": "300", "hashOfConfig": "267"}, {"size": 504, "mtime": 1741698155885, "results": "301", "hashOfConfig": "267"}, {"size": 524, "mtime": 1738576037267, "results": "302", "hashOfConfig": "267"}, {"size": 0, "mtime": 1738576037267, "results": "303", "hashOfConfig": "267"}, {"size": 1013, "mtime": 1741698155591, "results": "304", "hashOfConfig": "267"}, {"size": 7561, "mtime": 1747915795798, "results": "305", "hashOfConfig": "267"}, {"size": 2127, "mtime": 1738576037238, "results": "306", "hashOfConfig": "267"}, {"size": 1827, "mtime": 1746717131348, "results": "307", "hashOfConfig": "267"}, {"size": 7101, "mtime": 1746717131350, "results": "308", "hashOfConfig": "267"}, {"size": 11571, "mtime": 1746717131350, "results": "309", "hashOfConfig": "267"}, {"size": 597, "mtime": 1739442857325, "results": "310", "hashOfConfig": "267"}, {"size": 4728, "mtime": 1739453881148, "results": "311", "hashOfConfig": "267"}, {"size": 19454, "mtime": 1747049840281, "results": "312", "hashOfConfig": "267"}, {"size": 23835, "mtime": 1747049840285, "results": "313", "hashOfConfig": "267"}, {"size": 1301, "mtime": 1747049840319, "results": "314", "hashOfConfig": "267"}, {"size": 11652, "mtime": 1747049840320, "results": "315", "hashOfConfig": "267"}, {"size": 2808, "mtime": 1742236744834, "results": "316", "hashOfConfig": "267"}, {"size": 18734, "mtime": 1747049840335, "results": "317", "hashOfConfig": "267"}, {"size": 13199, "mtime": 1741965017447, "results": "318", "hashOfConfig": "267"}, {"size": 1330, "mtime": 1740670529590, "results": "319", "hashOfConfig": "267"}, {"size": 3122, "mtime": 1741965017449, "results": "320", "hashOfConfig": "267"}, {"size": 4550, "mtime": 1741964866499, "results": "321", "hashOfConfig": "267"}, {"size": 12733, "mtime": 1741965017450, "results": "322", "hashOfConfig": "267"}, {"size": 9001, "mtime": 1741965017450, "results": "323", "hashOfConfig": "267"}, {"size": 2506, "mtime": 1747661377395, "results": "324", "hashOfConfig": "267"}, {"size": 5240, "mtime": 1747661377498, "results": "325", "hashOfConfig": "267"}, {"size": 1515, "mtime": 1746717131368, "results": "326", "hashOfConfig": "267"}, {"size": 3991, "mtime": 1746717131368, "results": "327", "hashOfConfig": "267"}, {"size": 3162, "mtime": 1738576037269, "results": "328", "hashOfConfig": "267"}, {"size": 2662, "mtime": 1740670529595, "results": "329", "hashOfConfig": "267"}, {"size": 2966, "mtime": 1740670529596, "results": "330", "hashOfConfig": "267"}, {"size": 923, "mtime": 1740752742609, "results": "331", "hashOfConfig": "267"}, {"size": 1828, "mtime": 1747049840642, "results": "332", "hashOfConfig": "267"}, {"size": 6404, "mtime": 1746717131371, "results": "333", "hashOfConfig": "267"}, {"size": 310, "mtime": 1747049840641, "results": "334", "hashOfConfig": "267"}, {"size": 1498, "mtime": 1747049840641, "results": "335", "hashOfConfig": "267"}, {"size": 9075, "mtime": 1740670529596, "results": "336", "hashOfConfig": "267"}, {"size": 4509, "mtime": 1747049840644, "results": "337", "hashOfConfig": "267"}, {"size": 9124, "mtime": 1747049840645, "results": "338", "hashOfConfig": "267"}, {"size": 6193, "mtime": 1738576037270, "results": "339", "hashOfConfig": "267"}, {"size": 6804, "mtime": 1738576037271, "results": "340", "hashOfConfig": "267"}, {"size": 15042, "mtime": 1747915795877, "results": "341", "hashOfConfig": "267"}, {"size": 514, "mtime": 1738576037272, "results": "342", "hashOfConfig": "267"}, {"size": 1427, "mtime": 1738576037272, "results": "343", "hashOfConfig": "267"}, {"size": 6104, "mtime": 1747661378609, "results": "344", "hashOfConfig": "267"}, {"size": 7886, "mtime": 1743610636154, "results": "345", "hashOfConfig": "267"}, {"size": 6807, "mtime": 1741964866594, "results": "346", "hashOfConfig": "267"}, {"size": 2413, "mtime": 1738576037274, "results": "347", "hashOfConfig": "267"}, {"size": 3775, "mtime": 1746717131372, "results": "348", "hashOfConfig": "267"}, {"size": 6536, "mtime": 1742236745000, "results": "349", "hashOfConfig": "267"}, {"size": 3633, "mtime": 1738576037275, "results": "350", "hashOfConfig": "267"}, {"size": 3190, "mtime": 1738576037276, "results": "351", "hashOfConfig": "267"}, {"size": 551, "mtime": 1738576037276, "results": "352", "hashOfConfig": "267"}, {"size": 1766, "mtime": 1738576037276, "results": "353", "hashOfConfig": "267"}, {"size": 21136, "mtime": 1747915795900, "results": "354", "hashOfConfig": "267"}, {"size": 9254, "mtime": 1747059457560, "results": "355", "hashOfConfig": "267"}, {"size": 1220, "mtime": 1747915795901, "results": "356", "hashOfConfig": "267"}, {"size": 4527, "mtime": 1747661378659, "results": "357", "hashOfConfig": "267"}, {"size": 4126, "mtime": 1747049840664, "results": "358", "hashOfConfig": "267"}, {"size": 8865, "mtime": 1747049840665, "results": "359", "hashOfConfig": "267"}, {"size": 16230, "mtime": 1747049840666, "results": "360", "hashOfConfig": "267"}, {"size": 5042, "mtime": 1747049840668, "results": "361", "hashOfConfig": "267"}, {"size": 15681, "mtime": 1747049840669, "results": "362", "hashOfConfig": "267"}, {"size": 15159, "mtime": 1738576037284, "results": "363", "hashOfConfig": "267"}, {"size": 5640, "mtime": 1738576037284, "results": "364", "hashOfConfig": "267"}, {"size": 3208, "mtime": 1738576037285, "results": "365", "hashOfConfig": "267"}, {"size": 6713, "mtime": 1738576037285, "results": "366", "hashOfConfig": "267"}, {"size": 6958, "mtime": 1738576037286, "results": "367", "hashOfConfig": "267"}, {"size": 16549, "mtime": 1746717131377, "results": "368", "hashOfConfig": "267"}, {"size": 5231, "mtime": 1746717131382, "results": "369", "hashOfConfig": "267"}, {"size": 5041, "mtime": 1746717131383, "results": "370", "hashOfConfig": "267"}, {"size": 4433, "mtime": 1746717131383, "results": "371", "hashOfConfig": "267"}, {"size": 2976, "mtime": 1746717131384, "results": "372", "hashOfConfig": "267"}, {"size": 826, "mtime": 1738576037290, "results": "373", "hashOfConfig": "267"}, {"size": 1626, "mtime": 1738576037290, "results": "374", "hashOfConfig": "267"}, {"size": 13634, "mtime": 1747661378983, "results": "375", "hashOfConfig": "267"}, {"size": 2769, "mtime": 1747661378984, "results": "376", "hashOfConfig": "267"}, {"size": 31390, "mtime": 1747915795926, "results": "377", "hashOfConfig": "267"}, {"size": 3963, "mtime": 1747661379336, "results": "378", "hashOfConfig": "267"}, {"size": 4063, "mtime": 1747661379505, "results": "379", "hashOfConfig": "267"}, {"size": 7908, "mtime": 1747661379527, "results": "380", "hashOfConfig": "267"}, {"size": 534, "mtime": 1746717131389, "results": "381", "hashOfConfig": "267"}, {"size": 5010, "mtime": 1738576037293, "results": "382", "hashOfConfig": "267"}, {"size": 2792, "mtime": 1746717131389, "results": "383", "hashOfConfig": "267"}, {"size": 579, "mtime": 1738576037293, "results": "384", "hashOfConfig": "267"}, {"size": 1646, "mtime": 1741698156045, "results": "385", "hashOfConfig": "267"}, {"size": 3547, "mtime": 1742236745082, "results": "386", "hashOfConfig": "267"}, {"size": 3833, "mtime": 1746717131389, "results": "387", "hashOfConfig": "267"}, {"size": 22001, "mtime": 1746717131392, "results": "388", "hashOfConfig": "267"}, {"size": 2093, "mtime": 1740581981921, "results": "389", "hashOfConfig": "267"}, {"size": 2210, "mtime": 1738576037296, "results": "390", "hashOfConfig": "267"}, {"size": 2886, "mtime": 1738576037297, "results": "391", "hashOfConfig": "267"}, {"size": 5831, "mtime": 1746717131395, "results": "392", "hashOfConfig": "267"}, {"size": 1908, "mtime": 1746717131396, "results": "393", "hashOfConfig": "267"}, {"size": 7120, "mtime": 1746717131396, "results": "394", "hashOfConfig": "267"}, {"size": 2041, "mtime": 1738576037297, "results": "395", "hashOfConfig": "267"}, {"size": 2549, "mtime": 1738576037298, "results": "396", "hashOfConfig": "267"}, {"size": 9516, "mtime": 1746717131398, "results": "397", "hashOfConfig": "267"}, {"size": 3761, "mtime": 1746717131399, "results": "398", "hashOfConfig": "267"}, {"size": 5859, "mtime": 1746717131399, "results": "399", "hashOfConfig": "267"}, {"size": 3669, "mtime": 1747049840726, "results": "400", "hashOfConfig": "267"}, {"size": 3498, "mtime": 1747049840728, "results": "401", "hashOfConfig": "267"}, {"size": 2899, "mtime": 1741964866655, "results": "402", "hashOfConfig": "267"}, {"size": 11006, "mtime": 1747661379566, "results": "403", "hashOfConfig": "267"}, {"size": 5581, "mtime": 1738576037301, "results": "404", "hashOfConfig": "267"}, {"size": 7317, "mtime": 1741964866658, "results": "405", "hashOfConfig": "267"}, {"size": 8181, "mtime": 1741964866665, "results": "406", "hashOfConfig": "267"}, {"size": 13163, "mtime": 1747049840741, "results": "407", "hashOfConfig": "267"}, {"size": 5404, "mtime": 1738576037303, "results": "408", "hashOfConfig": "267"}, {"size": 5706, "mtime": 1738576037303, "results": "409", "hashOfConfig": "267"}, {"size": 2200, "mtime": 1738576037305, "results": "410", "hashOfConfig": "267"}, {"size": 10509, "mtime": 1746717131402, "results": "411", "hashOfConfig": "267"}, {"size": 1082, "mtime": 1738576037306, "results": "412", "hashOfConfig": "267"}, {"size": 21113, "mtime": 1747661379588, "results": "413", "hashOfConfig": "267"}, {"size": 1250, "mtime": 1741698156227, "results": "414", "hashOfConfig": "267"}, {"size": 8989, "mtime": 1738576037307, "results": "415", "hashOfConfig": "267"}, {"size": 2137, "mtime": 1738576037308, "results": "416", "hashOfConfig": "267"}, {"size": 1991, "mtime": 1742236745185, "results": "417", "hashOfConfig": "267"}, {"size": 14131, "mtime": 1747661379754, "results": "418", "hashOfConfig": "267"}, {"size": 9032, "mtime": 1738576037309, "results": "419", "hashOfConfig": "267"}, {"size": 5206, "mtime": 1738576037309, "results": "420", "hashOfConfig": "267"}, {"size": 1066, "mtime": 1738576037310, "results": "421", "hashOfConfig": "267"}, {"size": 943, "mtime": 1746717131408, "results": "422", "hashOfConfig": "267"}, {"size": 1981, "mtime": 1738576037311, "results": "423", "hashOfConfig": "267"}, {"size": 11781, "mtime": 1746717131409, "results": "424", "hashOfConfig": "267"}, {"size": 2177, "mtime": 1738576037313, "results": "425", "hashOfConfig": "267"}, {"size": 18692, "mtime": 1738576037315, "results": "426", "hashOfConfig": "267"}, {"size": 121511, "mtime": 1738576037316, "results": "427", "hashOfConfig": "267"}, {"size": 1324, "mtime": 1747661379756, "results": "428", "hashOfConfig": "267"}, {"size": 1846, "mtime": 1741698155590, "results": "429", "hashOfConfig": "267"}, {"size": 1395, "mtime": 1747049840206, "results": "430", "hashOfConfig": "267"}, {"size": 1607, "mtime": 1747049840213, "results": "431", "hashOfConfig": "267"}, {"size": 860, "mtime": 1741698155613, "results": "432", "hashOfConfig": "267"}, {"size": 1848, "mtime": 1747049840248, "results": "433", "hashOfConfig": "267"}, {"size": 3617, "mtime": 1746717131342, "results": "434", "hashOfConfig": "267"}, {"size": 10780, "mtime": 1747049840249, "results": "435", "hashOfConfig": "267"}, {"size": 9663, "mtime": 1747661377216, "results": "436", "hashOfConfig": "267"}, {"size": 7064, "mtime": 1747049840256, "results": "437", "hashOfConfig": "267"}, {"size": 5107, "mtime": 1742236744749, "results": "438", "hashOfConfig": "267"}, {"size": 193, "mtime": 1741964866408, "results": "439", "hashOfConfig": "267"}, {"size": 236, "mtime": 1741698155594, "results": "440", "hashOfConfig": "267"}, {"size": 241, "mtime": 1741698155594, "results": "441", "hashOfConfig": "267"}, {"size": 3681, "mtime": 1741698155596, "results": "442", "hashOfConfig": "267"}, {"size": 879, "mtime": 1741698155611, "results": "443", "hashOfConfig": "267"}, {"size": 567, "mtime": 1741698155610, "results": "444", "hashOfConfig": "267"}, {"size": 6182, "mtime": 1747049840216, "results": "445", "hashOfConfig": "267"}, {"size": 19659, "mtime": 1747049840225, "results": "446", "hashOfConfig": "267"}, {"size": 5798, "mtime": 1747049840234, "results": "447", "hashOfConfig": "267"}, {"size": 6060, "mtime": 1747049840242, "results": "448", "hashOfConfig": "267"}, {"size": 757, "mtime": 1741698155622, "results": "449", "hashOfConfig": "267"}, {"size": 16333, "mtime": 1747049840711, "results": "450", "hashOfConfig": "267"}, {"size": 4106, "mtime": 1747661378371, "results": "451", "hashOfConfig": "267"}, {"size": 2066, "mtime": 1747915795816, "results": "452", "hashOfConfig": "267"}, {"size": 2124, "mtime": 1747049840640, "results": "453", "hashOfConfig": "267"}, {"size": 17238, "mtime": 1747915795818, "results": "454", "hashOfConfig": "267"}, {"size": 5452, "mtime": 1747915795814, "results": "455", "hashOfConfig": "267"}, {"size": 6623, "mtime": 1747661379568, "results": "456", "hashOfConfig": "267"}, {"size": 1529, "mtime": 1741698156225, "results": "457", "hashOfConfig": "267"}, {"size": 616, "mtime": 1747049840745, "results": "458", "hashOfConfig": "267"}, {"size": 487, "mtime": 1741964866684, "results": "459", "hashOfConfig": "267"}, {"size": 1550, "mtime": 1741964866553, "results": "460", "hashOfConfig": "267"}, {"size": 6888, "mtime": 1746717131395, "results": "461", "hashOfConfig": "267"}, {"size": 6781, "mtime": 1742236745040, "results": "462", "hashOfConfig": "267"}, {"size": 7017, "mtime": 1747915795903, "results": "463", "hashOfConfig": "267"}, {"size": 1540, "mtime": 1742236745169, "results": "464", "hashOfConfig": "267"}, {"size": 7443, "mtime": 1746717131352, "results": "465", "hashOfConfig": "267"}, {"size": 6206, "mtime": 1747049840752, "results": "466", "hashOfConfig": "267"}, {"size": 1361, "mtime": 1742236745202, "results": "467", "hashOfConfig": "267"}, {"size": 14622, "mtime": 1747049840754, "results": "468", "hashOfConfig": "267"}, {"size": 427, "mtime": 1742236745243, "results": "469", "hashOfConfig": "267"}, {"size": 19774, "mtime": 1747661379548, "results": "470", "hashOfConfig": "267"}, {"size": 7687, "mtime": 1747915795797, "results": "471", "hashOfConfig": "267"}, {"size": 19784, "mtime": 1747049840661, "results": "472", "hashOfConfig": "267"}, {"size": 9647, "mtime": 1746717131376, "results": "473", "hashOfConfig": "267"}, {"size": 1287, "mtime": 1743610636163, "results": "474", "hashOfConfig": "267"}, {"size": 4592, "mtime": 1746717131377, "results": "475", "hashOfConfig": "267"}, {"size": 374, "mtime": 1743610636251, "results": "476", "hashOfConfig": "267"}, {"size": 405, "mtime": 1747049840354, "results": "477", "hashOfConfig": "267"}, {"size": 996, "mtime": 1747049840366, "results": "478", "hashOfConfig": "267"}, {"size": 1186, "mtime": 1747049840246, "results": "479", "hashOfConfig": "267"}, {"size": 2737, "mtime": 1747049840245, "results": "480", "hashOfConfig": "267"}, {"size": 826, "mtime": 1747049840201, "results": "481", "hashOfConfig": "267"}, {"size": 743, "mtime": 1747049840204, "results": "482", "hashOfConfig": "267"}, {"size": 55287, "mtime": 1747661377358, "results": "483", "hashOfConfig": "267"}, {"size": 148, "mtime": 1747049840271, "results": "484", "hashOfConfig": "267"}, {"size": 824, "mtime": 1747049840670, "results": "485", "hashOfConfig": "267"}, {"size": 7526, "mtime": 1747915795901, "results": "486", "hashOfConfig": "267"}, {"size": 259, "mtime": 1747049840674, "results": "487", "hashOfConfig": "267"}, {"size": 3358, "mtime": 1747049840713, "results": "488", "hashOfConfig": "267"}, {"size": 3111, "mtime": 1747049840722, "results": "489", "hashOfConfig": "267"}, {"size": 2060, "mtime": 1747049840733, "results": "490", "hashOfConfig": "267"}, {"size": 1571, "mtime": 1747049840212, "results": "491", "hashOfConfig": "267"}, {"size": 62344, "mtime": 1747049840704, "results": "492", "hashOfConfig": "267"}, {"size": 1225, "mtime": 1747049840673, "results": "493", "hashOfConfig": "267"}, {"size": 29390, "mtime": 1747661378884, "results": "494", "hashOfConfig": "267"}, {"size": 3357, "mtime": 1747049840696, "results": "495", "hashOfConfig": "267"}, {"size": 7172, "mtime": 1747661379668, "results": "496", "hashOfConfig": "267"}, {"size": 41730, "mtime": 1747661377525, "results": "497", "hashOfConfig": "267"}, {"size": 15791, "mtime": 1747661377586, "results": "498", "hashOfConfig": "267"}, {"size": 9485, "mtime": 1747915795800, "results": "499", "hashOfConfig": "267"}, {"size": 28273, "mtime": 1747661377603, "results": "500", "hashOfConfig": "267"}, {"size": 5564, "mtime": 1747661377604, "results": "501", "hashOfConfig": "267"}, {"size": 6061, "mtime": 1747661377670, "results": "502", "hashOfConfig": "267"}, {"size": 15577, "mtime": 1747661377729, "results": "503", "hashOfConfig": "267"}, {"size": 17208, "mtime": 1747661377734, "results": "504", "hashOfConfig": "267"}, {"size": 38747, "mtime": 1747661377733, "results": "505", "hashOfConfig": "267"}, {"size": 40501, "mtime": 1747661377736, "results": "506", "hashOfConfig": "267"}, {"size": 9557, "mtime": 1747661377737, "results": "507", "hashOfConfig": "267"}, {"size": 12418, "mtime": 1747661377738, "results": "508", "hashOfConfig": "267"}, {"size": 27733, "mtime": 1747661377817, "results": "509", "hashOfConfig": "267"}, {"size": 27090, "mtime": 1747661377880, "results": "510", "hashOfConfig": "267"}, {"size": 17928, "mtime": 1747661378098, "results": "511", "hashOfConfig": "267"}, {"size": 24604, "mtime": 1747661378120, "results": "512", "hashOfConfig": "267"}, {"size": 5461, "mtime": 1747661378200, "results": "513", "hashOfConfig": "267"}, {"size": 16115, "mtime": 1747661378204, "results": "514", "hashOfConfig": "267"}, {"size": 9305, "mtime": 1747661378203, "results": "515", "hashOfConfig": "267"}, {"size": 26124, "mtime": 1747661378206, "results": "516", "hashOfConfig": "267"}, {"size": 10971, "mtime": 1747661377503, "results": "517", "hashOfConfig": "267"}, {"size": 1142, "mtime": 1747661378251, "results": "518", "hashOfConfig": "267"}, {"size": 760, "mtime": 1747661378253, "results": "519", "hashOfConfig": "267"}, {"size": 3847, "mtime": 1747661378372, "results": "520", "hashOfConfig": "267"}, {"size": 1531, "mtime": 1747661378373, "results": "521", "hashOfConfig": "267"}, {"size": 621, "mtime": 1747661377359, "results": "522", "hashOfConfig": "267"}, {"size": 3180, "mtime": 1747661378540, "results": "523", "hashOfConfig": "267"}, {"size": 5304, "mtime": 1747661378610, "results": "524", "hashOfConfig": "267"}, {"size": 4706, "mtime": 1747915795812, "results": "525", "hashOfConfig": "267"}, {"size": 1165, "mtime": 1747915795815, "results": "526", "hashOfConfig": "267"}, {"size": 2092, "mtime": 1747915795826, "results": "527", "hashOfConfig": "267"}, {"size": 21615, "mtime": 1747915815897, "results": "528", "hashOfConfig": "267"}, {"size": 2491, "mtime": 1747661379847, "results": "529", "hashOfConfig": "267"}, {"size": 2289, "mtime": 1747915795812, "results": "530", "hashOfConfig": "267"}, {"size": 18825, "mtime": 1747915815898, "results": "531", "hashOfConfig": "267"}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "vbfoca", {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "997", "messages": "998", "suppressedMessages": "999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1000", "messages": "1001", "suppressedMessages": "1002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1003", "messages": "1004", "suppressedMessages": "1005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1006", "messages": "1007", "suppressedMessages": "1008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1009", "messages": "1010", "suppressedMessages": "1011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1012", "messages": "1013", "suppressedMessages": "1014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1015", "messages": "1016", "suppressedMessages": "1017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1018", "messages": "1019", "suppressedMessages": "1020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1021", "messages": "1022", "suppressedMessages": "1023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1024", "messages": "1025", "suppressedMessages": "1026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1027", "messages": "1028", "suppressedMessages": "1029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1030", "messages": "1031", "suppressedMessages": "1032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1033", "messages": "1034", "suppressedMessages": "1035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1036", "messages": "1037", "suppressedMessages": "1038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1039", "messages": "1040", "suppressedMessages": "1041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1042", "messages": "1043", "suppressedMessages": "1044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1045", "messages": "1046", "suppressedMessages": "1047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1048", "messages": "1049", "suppressedMessages": "1050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1051", "messages": "1052", "suppressedMessages": "1053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1054", "messages": "1055", "suppressedMessages": "1056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1057", "messages": "1058", "suppressedMessages": "1059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1060", "messages": "1061", "suppressedMessages": "1062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1063", "messages": "1064", "suppressedMessages": "1065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1066", "messages": "1067", "suppressedMessages": "1068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1069", "messages": "1070", "suppressedMessages": "1071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1072", "messages": "1073", "suppressedMessages": "1074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1075", "messages": "1076", "suppressedMessages": "1077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1078", "messages": "1079", "suppressedMessages": "1080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1081", "messages": "1082", "suppressedMessages": "1083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1084", "messages": "1085", "suppressedMessages": "1086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1087", "messages": "1088", "suppressedMessages": "1089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1090", "messages": "1091", "suppressedMessages": "1092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1093", "messages": "1094", "suppressedMessages": "1095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1096", "messages": "1097", "suppressedMessages": "1098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1099", "messages": "1100", "suppressedMessages": "1101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1102", "messages": "1103", "suppressedMessages": "1104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1105", "messages": "1106", "suppressedMessages": "1107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1108", "messages": "1109", "suppressedMessages": "1110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1111", "messages": "1112", "suppressedMessages": "1113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1114", "messages": "1115", "suppressedMessages": "1116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1117", "messages": "1118", "suppressedMessages": "1119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1120", "messages": "1121", "suppressedMessages": "1122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1123", "messages": "1124", "suppressedMessages": "1125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1126", "messages": "1127", "suppressedMessages": "1128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1129", "messages": "1130", "suppressedMessages": "1131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1132", "messages": "1133", "suppressedMessages": "1134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1135", "messages": "1136", "suppressedMessages": "1137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1138", "messages": "1139", "suppressedMessages": "1140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1141", "messages": "1142", "suppressedMessages": "1143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1144", "messages": "1145", "suppressedMessages": "1146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1147", "messages": "1148", "suppressedMessages": "1149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1150", "messages": "1151", "suppressedMessages": "1152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1153", "messages": "1154", "suppressedMessages": "1155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1156", "messages": "1157", "suppressedMessages": "1158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1159", "messages": "1160", "suppressedMessages": "1161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1162", "messages": "1163", "suppressedMessages": "1164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1165", "messages": "1166", "suppressedMessages": "1167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1168", "messages": "1169", "suppressedMessages": "1170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1171", "messages": "1172", "suppressedMessages": "1173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1174", "messages": "1175", "suppressedMessages": "1176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1177", "messages": "1178", "suppressedMessages": "1179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1180", "messages": "1181", "suppressedMessages": "1182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1183", "messages": "1184", "suppressedMessages": "1185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1186", "messages": "1187", "suppressedMessages": "1188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1189", "messages": "1190", "suppressedMessages": "1191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1192", "messages": "1193", "suppressedMessages": "1194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1195", "messages": "1196", "suppressedMessages": "1197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1198", "messages": "1199", "suppressedMessages": "1200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1201", "messages": "1202", "suppressedMessages": "1203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1204", "messages": "1205", "suppressedMessages": "1206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1207", "messages": "1208", "suppressedMessages": "1209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1210", "messages": "1211", "suppressedMessages": "1212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1213", "messages": "1214", "suppressedMessages": "1215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1216", "messages": "1217", "suppressedMessages": "1218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1219", "messages": "1220", "suppressedMessages": "1221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1222", "messages": "1223", "suppressedMessages": "1224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1225", "messages": "1226", "suppressedMessages": "1227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1228", "messages": "1229", "suppressedMessages": "1230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1231", "messages": "1232", "suppressedMessages": "1233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1234", "messages": "1235", "suppressedMessages": "1236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1237", "messages": "1238", "suppressedMessages": "1239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1240", "messages": "1241", "suppressedMessages": "1242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1243", "messages": "1244", "suppressedMessages": "1245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1246", "messages": "1247", "suppressedMessages": "1248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1249", "messages": "1250", "suppressedMessages": "1251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1252", "messages": "1253", "suppressedMessages": "1254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1255", "messages": "1256", "suppressedMessages": "1257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1258", "messages": "1259", "suppressedMessages": "1260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1261", "messages": "1262", "suppressedMessages": "1263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1264", "messages": "1265", "suppressedMessages": "1266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1267", "messages": "1268", "suppressedMessages": "1269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1270", "messages": "1271", "suppressedMessages": "1272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1273", "messages": "1274", "suppressedMessages": "1275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1276", "messages": "1277", "suppressedMessages": "1278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1279", "messages": "1280", "suppressedMessages": "1281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1282", "messages": "1283", "suppressedMessages": "1284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1285", "messages": "1286", "suppressedMessages": "1287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1288", "messages": "1289", "suppressedMessages": "1290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1291", "messages": "1292", "suppressedMessages": "1293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1294", "messages": "1295", "suppressedMessages": "1296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1297", "messages": "1298", "suppressedMessages": "1299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1300", "messages": "1301", "suppressedMessages": "1302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1303", "messages": "1304", "suppressedMessages": "1305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1306", "messages": "1307", "suppressedMessages": "1308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1309", "messages": "1310", "suppressedMessages": "1311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1312", "messages": "1313", "suppressedMessages": "1314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1315", "messages": "1316", "suppressedMessages": "1317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1318", "messages": "1319", "suppressedMessages": "1320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1321", "messages": "1322", "suppressedMessages": "1323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1324", "messages": "1325", "suppressedMessages": "1326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\api\\contact\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\AccountRole.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\AccountState.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\BanksList.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\constant.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\ConstStatus.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\InquiryState.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\MarocRegions.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\NotificationNavCode.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\PaymentMethodConfig.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\config\\ProductStatus.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\context\\AuthContext.jsx", ["1327"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\context\\GlobalStateContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\helpers\\helpers.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\hook\\useCountdown.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\hook\\useDeviceScale.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\hook\\useIsMobile.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\layout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\lib\\apiService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\lib\\axiosInstance.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\lib\\notion.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\loading.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\not-found.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\adminService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\authService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\brandService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\cartService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\categoryService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\orderService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\packageService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\paymentService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\profileService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\specificationService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\ticketService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\utils\\dateFormatter.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\utils\\errorHandler.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\utils\\helperFunctions.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\about-us\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\cart\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\cart\\payment\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\data-deletion\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\domains\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\hosting-plans\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\layout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\orders\\page.jsx", ["1328"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\payment-history\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\payments\\[paymentId]\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\profile\\edit\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\profile\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\sidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\ssl-certificates\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\support\\add-ticket\\page.jsx", ["1329"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\support\\announcements\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\support\\layout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\support\\sidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\support\\tickets\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\support\\tickets\\[ticketId]\\page.jsx", ["1330"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\layout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\about-us\\banner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\about-us\\ourMission.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\about-us\\whyChooseUs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\AdminFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\AdminTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\ConfirmationModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\package\\packageFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\package\\PreviewPackage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\SearchHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\SearchInput.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\UserModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ai-services\\aiIntro.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ai-services\\aiMain.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ai-services\\whatWeOffers.jsx", ["1331"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ai-services\\whatWeOffers2.jsx", ["1332"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\auth\\avatarWithUserDropdown.jsx", ["1333"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\auth\\emailVerification.jsx", ["1334"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\auth\\googleLoginButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\auth\\login.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\auth\\register.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\auth\\resetPassword.jsx", ["1335"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\auth\\sharedBetweenAuth.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\auth\\socialLogin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\avatar\\AvatarIcon.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\blog\\blogPostContent.jsx", ["1336", "1337"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\blog\\postCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\blog\\recentPosts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\blog\\search.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cart\\billingInfoForm.jsx", ["1338"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cart\\cartItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cart\\cartItemsList.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cart\\summary.jsx", ["1339"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cloud-maroc\\cloudMarocIntro.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cloud-maroc\\cloudVps.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cloud-maroc\\sharedCloud.jsx", ["1340"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cloud-security\\cloudSecurityIntro.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cloud-security\\cloudSecurityMain.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cloud-security\\offersSwiper.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cloud-security\\offersSwiper2.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\guide\\banner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\guide\\hostingAdvantages.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\guide\\ourSolutionsGuide.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\guide\\whyChooseUs.jsx", ["1341"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\aboutUs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\banner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\chatWithUs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\cloudAdvantages.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\cloudAdvantagesDesktop.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\cloudAdvantagesMobile.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\cloudMoroc.jsx", ["1342", "1343", "1344", "1345", "1346", "1347", "1348", "1349", "1350"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\header.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\mainNavbar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\mobileCarousel.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\otherServices.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\services.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\siteBranding.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\testimonials.jsx", ["1351"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\topNavbar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\topNavbarcopy.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\TypingAnimation.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\whotrustUs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\whyChooseUs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\hosting\\hostingPricingTable.jsx", ["1352"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\hosting\\intro.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-a\\banner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-a\\callToAction.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-a\\PopularOffers.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-a\\testimonials.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-a\\testimonials2.jsx", ["1353", "1354"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-a\\whyTrustUs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-b\\banner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-b\\discountOffer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\landing\\landing-b\\servicesPense.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\local-switcher.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\managed-services\\managedServicesIntro.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\managed-services\\managedServicesMain.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\order\\paymentStatusModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\pdf\\InvoiceTemplate.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\profile\\avatarUploader.jsx", ["1355"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\profile\\editEmailForm.jsx", ["1356"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\profile\\editPasswordForm.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\profile\\editProfileForm.jsx", ["1357"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\profile\\inputOneTimePassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\promotion\\promotionIntro.jsx", ["1358"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\servers\\serversIntro.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\servers\\serversMain.jsx", ["1359"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\alertCustom.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\contactForm2.jsx", ["1360"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\customButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\customSwiper.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\customSwiper2.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\faq.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\footer2.jsx", ["1361"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\landingTestimonials.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\navDropDown.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\notification.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\whatsAppFloatingButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ssl-old\\sslIntro.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ssl-old\\sslMain.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\web-dev\\webDevIntro.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\web-dev\\webDevPricingTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\icons\\svgIcons.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\middleware.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\404\\page.jsx", ["1362"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\ai-services\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\cloud-security\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\guide\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\managed-services\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\privacy-policy\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\ssl\\page.jsx", ["1363"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\terms-of-service\\page.jsx", ["1364"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\web-development\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\auth\\forgotPassword\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\auth\\login\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\auth\\register\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\auth\\reset-password\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\auth\\verification\\page.jsx", ["1365"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\blog\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\blog\\[slug]\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\hosting\\dedicated\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\hosting\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\hosting\\shared\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\hosting\\vps\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\hosting\\vps2\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\hosting\\pricingPlanGrid.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\chatbotService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\Chatbot\\ChatbotButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\Chatbot\\ChatbotContainer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\Chatbot\\ChatbotWindow.jsx", ["1366"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\Chatbot\\ChatMessage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\chatbot\\chatbot.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\chatbot\\chatMessage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\FloatingButtonsContainer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\errorAlert.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\ImageModal.jsx", ["1367"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\hosting\\pricingPlanGridWeb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\banner2.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\homeBanner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\countdown.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\web-development\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ssl\\SSLCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ssl\\SSLFeatures.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ssl\\SSLFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\ssl\\SSLHero.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\hosting\\hPackagesPlanGrid.jsx", ["1368"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\cart\\page.jsx", ["1369"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cart2\\billingInfoForm.jsx", ["1370"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cart2\\cartItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cart2\\cartItemsList.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\cart2\\summary.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\SecButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\hook\\useDebounce.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\sslService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\landing\\_demandez-devis\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\landing\\_demandez-devis-b\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\_promotion\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\_servers\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\ssl-certificates\\activate\\[id]\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\client\\_page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\CTAButtons.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\DomaineSearch.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\Section.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\managed-services\\ManagedServicesDetails.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\managed-services\\ManagedServicesReasons.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\profile\\companyInfoForm.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\(sharedPages)\\cloud-maroc\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\companyIntro.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\DomainExtensionDropdown.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\TestimonialsSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\WhoTrustUs2.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\shared\\faq2.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\activity-logs\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\chats\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\chats\\[id]\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\jobs\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\layout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\login\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\marketing\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\orders\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\orders\\[orderId]\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\packages\\packageForm.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\packages\\packagesTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\packages\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\page.jsx", ["1371"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\pages\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\posts\\page.jsx", ["1372", "1373"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\settings\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\sidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\support\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\support\\[ticketId]\\page.jsx", ["1374"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\users\\page.jsx", ["1375", "1376"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\admin\\_page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\global-error.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\hook\\useIsMobile.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\utils\\logFormatter.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\utils\\pendingCartHandler.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\[locale]\\layout.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\admin\\ChatbotContextModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\avatar\\UserAvatar.jsx", ["1377"], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\Chatbot\\AddToCartButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\Chatbot\\ChatRecommendations.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\Chatbot\\ConfirmDialog.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\home\\Notifications.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\services\\adminService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\app\\services\\userService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Work\\ztech_new_env\\ztech_dev\\frontend\\src\\components\\user\\UserNotifications.jsx", [], [], {"ruleId": "1378", "severity": 1, "message": "1379", "line": 140, "column": 5, "nodeType": "1380", "endLine": 140, "endColumn": 59, "suggestions": "1381"}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 82, "column": 37, "nodeType": "1384", "endLine": 85, "endColumn": 91}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 111, "column": 13, "nodeType": "1384", "endLine": 117, "endColumn": 15}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 159, "column": 17, "nodeType": "1384", "endLine": 166, "endColumn": 19}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 29, "column": 45, "nodeType": "1384", "endLine": 33, "endColumn": 47}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 121, "column": 21, "nodeType": "1384", "endLine": 125, "endColumn": 23}, {"ruleId": "1378", "severity": 1, "message": "1385", "line": 68, "column": 6, "nodeType": "1380", "endLine": 68, "endColumn": 17, "suggestions": "1386"}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 8, "column": 13, "nodeType": "1384", "endLine": 8, "endColumn": 96}, {"ruleId": "1378", "severity": 1, "message": "1387", "line": 35, "column": 6, "nodeType": "1380", "endLine": 35, "endColumn": 31, "suggestions": "1388"}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 45, "column": 21, "nodeType": "1384", "endLine": 50, "endColumn": 23}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 85, "column": 21, "nodeType": "1384", "endLine": 89, "endColumn": 23}, {"ruleId": "1378", "severity": 1, "message": "1389", "line": 50, "column": 6, "nodeType": "1380", "endLine": 50, "endColumn": 12, "suggestions": "1390"}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 106, "column": 9, "nodeType": "1384", "endLine": 110, "endColumn": 11}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 394, "column": 19, "nodeType": "1384", "endLine": 399, "endColumn": 21}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 97, "column": 37, "nodeType": "1384", "endLine": 101, "endColumn": 39}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 41, "column": 9, "nodeType": "1384", "endLine": 45, "endColumn": 11}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 49, "column": 9, "nodeType": "1384", "endLine": 53, "endColumn": 11}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 59, "column": 9, "nodeType": "1384", "endLine": 66, "endColumn": 11}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 70, "column": 9, "nodeType": "1384", "endLine": 77, "endColumn": 11}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 85, "column": 9, "nodeType": "1384", "endLine": 92, "endColumn": 11}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 98, "column": 9, "nodeType": "1384", "endLine": 105, "endColumn": 11}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 113, "column": 9, "nodeType": "1384", "endLine": 120, "endColumn": 11}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 126, "column": 9, "nodeType": "1384", "endLine": 133, "endColumn": 11}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 207, "column": 13, "nodeType": "1384", "endLine": 211, "endColumn": 15}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 86, "column": 21, "nodeType": "1384", "endLine": 91, "endColumn": 23}, {"ruleId": "1378", "severity": 1, "message": "1391", "line": 44, "column": 8, "nodeType": "1380", "endLine": 44, "endColumn": 10, "suggestions": "1392"}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 87, "column": 21, "nodeType": "1384", "endLine": 91, "endColumn": 23}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 103, "column": 37, "nodeType": "1384", "endLine": 107, "endColumn": 39}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 122, "column": 17, "nodeType": "1384", "endLine": 126, "endColumn": 19}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 179, "column": 19, "nodeType": "1384", "endLine": 183, "endColumn": 21}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 284, "column": 29, "nodeType": "1384", "endLine": 288, "endColumn": 31}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 46, "column": 25, "nodeType": "1384", "endLine": 50, "endColumn": 27}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 104, "column": 25, "nodeType": "1384", "endLine": 109, "endColumn": 27}, {"ruleId": "1378", "severity": 1, "message": "1393", "line": 54, "column": 8, "nodeType": "1380", "endLine": 54, "endColumn": 14, "suggestions": "1394"}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 163, "column": 17, "nodeType": "1384", "endLine": 167, "endColumn": 19}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 19, "column": 21, "nodeType": "1384", "endLine": 23, "endColumn": 49}, {"ruleId": "1378", "severity": 1, "message": "1395", "line": 113, "column": 6, "nodeType": "1380", "endLine": 113, "endColumn": 34, "suggestions": "1396"}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 169, "column": 9, "nodeType": "1384", "endLine": 173, "endColumn": 11}, {"ruleId": "1378", "severity": 1, "message": "1397", "line": 35, "column": 6, "nodeType": "1380", "endLine": 35, "endColumn": 8, "suggestions": "1398"}, {"ruleId": "1378", "severity": 1, "message": "1399", "line": 152, "column": 6, "nodeType": "1380", "endLine": 152, "endColumn": 37, "suggestions": "1400"}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 22, "column": 15, "nodeType": "1384", "endLine": 26, "endColumn": 17}, {"ruleId": "1378", "severity": 1, "message": "1401", "line": 41, "column": 6, "nodeType": "1380", "endLine": 41, "endColumn": 8, "suggestions": "1402"}, {"ruleId": "1378", "severity": 1, "message": "1403", "line": 68, "column": 6, "nodeType": "1380", "endLine": 68, "endColumn": 8, "suggestions": "1404"}, {"ruleId": "1378", "severity": 1, "message": "1389", "line": 50, "column": 6, "nodeType": "1380", "endLine": 50, "endColumn": 12, "suggestions": "1405"}, {"ruleId": "1378", "severity": 1, "message": "1406", "line": 89, "column": 6, "nodeType": "1380", "endLine": 89, "endColumn": 24, "suggestions": "1407"}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 297, "column": 19, "nodeType": "1384", "endLine": 301, "endColumn": 21}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 369, "column": 25, "nodeType": "1384", "endLine": 373, "endColumn": 27}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 199, "column": 17, "nodeType": "1384", "endLine": 206, "endColumn": 19}, {"ruleId": "1378", "severity": 1, "message": "1408", "line": 74, "column": 6, "nodeType": "1380", "endLine": 80, "endColumn": 4, "suggestions": "1409"}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 345, "column": 35, "nodeType": "1384", "endLine": 354, "endColumn": 37}, {"ruleId": "1382", "severity": 1, "message": "1383", "line": 89, "column": 13, "nodeType": "1384", "endLine": 99, "endColumn": 15}, "react-hooks/exhaustive-deps", "React Hook useMemo has a missing dependency: 'logout'. Either include it or remove the dependency array.", "ArrayExpression", ["1410"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has missing dependencies: 'pathname' and 'setCartCount'. Either include them or remove the dependency array.", ["1411"], "React Hook useEffect has a missing dependency: 'router'. Either include it or remove the dependency array.", ["1412"], "React Hook useEffect has missing dependencies: 'cancelEditBillingInfo' and 'setBillingInfo'. Either include them or remove the dependency array. If 'setBillingInfo' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1413"], "React Hook useEffect has a missing dependency: 'hostingPacks'. Either include it or remove the dependency array.", ["1414"], "React Hook useEffect has a missing dependency: 'formData'. Either include it or remove the dependency array. You can also do a functional update 'setFormData(f => ...)' if you only need 'formData' in the 'setFormData' call.", ["1415"], "React Hook useMemo has missing dependencies: 'matchesBrand' and 'matchesSSLType'. Either include them or remove the dependency array.", ["1416"], "React Hook useEffect has missing dependencies: 'checkAuth' and 'searchParams'. Either include them or remove the dependency array.", ["1417"], "React Hook useEffect has a missing dependency: 'updateWindowHeight'. Either include it or remove the dependency array.", ["1418"], "React Hook useEffect has a missing dependency: 'brandName'. Either include it or remove the dependency array.", ["1419"], "React Hook useEffect has missing dependencies: 'cartCount' and 'fetchCartData'. Either include them or remove the dependency array.", ["1420"], ["1421"], "React Hook useEffect has a missing dependency: 'brandDistribution'. Either include it or remove the dependency array.", ["1422"], "React Hook useEffect has a missing dependency: 'fetchAdmins'. Either include it or remove the dependency array.", ["1423"], {"desc": "1424", "fix": "1425"}, {"desc": "1426", "fix": "1427"}, {"desc": "1428", "fix": "1429"}, {"desc": "1430", "fix": "1431"}, {"desc": "1432", "fix": "1433"}, {"desc": "1434", "fix": "1435"}, {"desc": "1436", "fix": "1437"}, {"desc": "1438", "fix": "1439"}, {"desc": "1440", "fix": "1441"}, {"desc": "1442", "fix": "1443"}, {"desc": "1444", "fix": "1445"}, {"desc": "1430", "fix": "1446"}, {"desc": "1447", "fix": "1448"}, {"desc": "1449", "fix": "1450"}, "Update the dependencies array to be: [user, loading, logout, cartCount, isAuthenticated]", {"range": "1451", "text": "1452"}, "Update the dependencies array to be: [cartCount, pathname, setCartCount]", {"range": "1453", "text": "1454"}, "Update the dependencies array to be: [userId, activationField, router]", {"range": "1455", "text": "1456"}, "Update the dependencies array to be: [cancelEditBillingInfo, setBillingInfo, user]", {"range": "1457", "text": "1458"}, "Update the dependencies array to be: [hostingPacks]", {"range": "1459", "text": "1460"}, "Update the dependencies array to be: [data, formData]", {"range": "1461", "text": "1462"}, "Update the dependencies array to be: [backendSSLPackages, filter.type, filter.value, matchesBrand, matchesSSLType]", {"range": "1463", "text": "1464"}, "Update the dependencies array to be: [checkAuth, searchParams]", {"range": "1465", "text": "1466"}, "Update the dependencies array to be: [messages, showRecommendations, updateWindowHeight]", {"range": "1467", "text": "1468"}, "Update the dependencies array to be: [brandName]", {"range": "1469", "text": "1470"}, "Update the dependencies array to be: [cartCount, fetchCartData]", {"range": "1471", "text": "1472"}, {"range": "1473", "text": "1458"}, "Update the dependencies array to be: [brandDistribution, selectedCategory]", {"range": "1474", "text": "1475"}, "Update the dependencies array to be: [currentPage, itemsPerPage, roleFilter, stateFilter, debouncedSearchQuery, fetchAdmins]", {"range": "1476", "text": "1477"}, [3860, 3914], "[user, loading, logout, cartCount, isAuthenticated]", [2424, 2435], "[cartCount, pathname, setCartCount]", [1193, 1218], "[userId, activationField, router]", [2000, 2006], "[cancelEditBillingInfo, setBillingInfo, user]", [7572, 7574], "[hostingPacks]", [2094, 2100], "[data, formData]", [4025, 4053], "[backendSSLPackages, filter.type, filter.value, matchesBrand, matchesSSLType]", [1137, 1139], "[check<PERSON><PERSON>, searchPara<PERSON>]", [6038, 6069], "[messages, showRecommendations, updateWindowHeight]", [1466, 1468], "[brandName]", [2199, 2201], "[cartCount, fetchCartData]", [1994, 2000], [2796, 2814], "[brandDistribution, selectedCategory]", [2693, 2798], "[currentPage, itemsPerPage, roleFilter, stateFilter, debouncedSearchQuery, fetchAdmins]"]