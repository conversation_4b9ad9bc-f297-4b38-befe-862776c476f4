const axios = require("axios");
const SubOrder = require("../models/SubOrder");
const Package = require("../models/Package");
const OrderStatus = require("../constants/enums/order-status");

// API configuration
const API_BASE_URL = "https://domain-name-api.dynv6.net/";
const AUTH_PARAMS = {
  "auth-userid": process.env.AUTH_USERID_PROD,
  "api-key": process.env.API_KEY_PROD,
};

// Company account for domain registrations
const COMPANY_ACCOUNT = {
  customerId: process.env.COMPANY_CUSTOMER_ID || "",
  regContactId: process.env.COMPANY_REG_CONTACT_ID || "",
  adminContactId: process.env.COMPANY_ADMIN_CONTACT_ID || "",
  techContactId: process.env.COMPANY_TECH_CONTACT_ID || "",
  billingContactId: process.env.COMPANY_BILLING_CONTACT_ID || "",
};

// Default nameservers
const DEFAULT_NAMESERVERS = [
  process.env.DEFAULT_NS1 || "ns1.domain-name-api.dynv6.net",
  process.env.DEFAULT_NS2 || "ns2.domain-name-api.dynv6.net",
];

/**
 * Process domain registration for an order
 * @param {Object} order - The order object with populated subOrders
 * @returns {Promise<Array>} - Array of registration results
 */
exports.processDomainRegistrations = async (order) => {
  try {
    console.log("🔄 Processing domain registrations for order:", order._id);
    
    if (!order.subOrders || order.subOrders.length === 0) {
      console.log("ℹ️ No suborders found in order");
      return [];
    }

    // Find domain suborders by checking if the package name contains a domain pattern
    const domainSubOrders = [];
    
    for (const subOrder of order.subOrders) {
      // Skip if package is not populated
      if (!subOrder.package) {
        console.log("⚠️ SubOrder has no package:", subOrder._id);
        continue;
      }
      
      // Check if this is a domain package by checking the reference
      if (subOrder.package.reference && subOrder.package.reference.startsWith('domain-')) {
        console.log("✅ Found domain suborder:", subOrder._id);
        domainSubOrders.push(subOrder);
      }
    }
    
    if (domainSubOrders.length === 0) {
      console.log("ℹ️ No domain suborders found in order");
      return [];
    }
    
    // Process each domain suborder
    const registrationResults = await Promise.all(
      domainSubOrders.map(async (subOrder) => {
        return await registerDomain(subOrder, order);
      })
    );
    
    return registrationResults;
  } catch (error) {
    console.error("❌ Error processing domain registrations:", error);
    throw error;
  }
};

/**
 * Register a domain for a specific suborder
 * @param {Object} subOrder - The suborder containing domain information
 * @param {Object} order - The parent order
 * @returns {Promise<Object>} - Registration result
 */
async function registerDomain(subOrder, order) {
  try {
    // Extract domain name from package name
    const domainName = subOrder.package.name;
    console.log("🔄 Registering domain:", domainName);
    
    // Validate company account configuration
    if (!COMPANY_ACCOUNT.customerId || 
        !COMPANY_ACCOUNT.regContactId || 
        !COMPANY_ACCOUNT.adminContactId || 
        !COMPANY_ACCOUNT.techContactId || 
        !COMPANY_ACCOUNT.billingContactId) {
      throw new Error("Company account not properly configured for domain registration");
    }
    
    // Prepare registration parameters
    const params = {
      ...AUTH_PARAMS,
      "domain-name": domainName,
      years: subOrder.period || 1,
      "customer-id": COMPANY_ACCOUNT.customerId,
      "reg-contact-id": COMPANY_ACCOUNT.regContactId,
      "admin-contact-id": COMPANY_ACCOUNT.adminContactId,
      "tech-contact-id": COMPANY_ACCOUNT.techContactId,
      "billing-contact-id": COMPANY_ACCOUNT.billingContactId,
      "invoice-option": "KeepInvoice",
      "auto-renew": false, // Default to false for auto-renewal
      ns: DEFAULT_NAMESERVERS,
      "purchase-privacy": true, // Enable privacy protection by default
      "protect-privacy": true,
    };
    
    // Call the domain registration API
    const response = await axios.post(
      `${API_BASE_URL}/domains/register.json`,
      null,
      { params }
    );
    
    console.log("✅ Domain registration successful:", response.data);
    
    // Update suborder status to ACTIVE
    await SubOrder.findByIdAndUpdate(
      subOrder._id,
      { status: OrderStatus.ACTIVE }
    );
    
    return {
      subOrderId: subOrder._id,
      domainName,
      success: true,
      registrationData: response.data,
    };
  } catch (error) {
    console.error(
      "❌ Domain registration error:",
      error.response?.data || error.message
    );
    
    return {
      subOrderId: subOrder._id,
      domainName: subOrder.package.name,
      success: false,
      error: error.response?.data || error.message,
    };
  }
}
