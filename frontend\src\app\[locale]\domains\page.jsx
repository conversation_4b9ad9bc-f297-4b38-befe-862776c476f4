"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { 
  Typo<PERSON>, 
  <PERSON><PERSON>,
  Card,
  CardBody,
} from "@material-tailwind/react";
import { 
  Globe, 
  Shield, 
  Lock, 
  Check, 
  Search,
  Server,
  RefreshCw,
  Zap,
  Award,
  Clock
} from "lucide-react";
import Image from "next/image";
import DomainSearch from "@/components/home/<USER>";

export default function DomainsPage() {
  const t = useTranslations("Home");
  const dt = useTranslations("client.domainWrapper");
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-blue-50">
      {/* Hero Section */}
      <section className="pt-16 pb-24 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h1" className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Find Your Perfect Domain Name
              </Typography>
              <Typography className="text-xl text-gray-600 max-w-3xl mx-auto">
                Secure your online identity with a domain name that represents your brand. 
                Start building your online presence today.
              </Typography>
            </motion.div>
          </div>

          {/* Domain Search Component */}
          <div className="mt-8">
            <DomainSearch t={t} />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <Typography variant="h2" className="text-3xl font-bold text-gray-900 mb-4">
              Why Choose Our Domain Services?
            </Typography>
            <Typography className="text-lg text-gray-600 max-w-3xl mx-auto">
              We provide comprehensive domain management solutions with competitive pricing and exceptional support.
            </Typography>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: Globe,
                title: "Wide TLD Selection",
                description: "Choose from hundreds of domain extensions to find the perfect fit for your brand.",
                color: "bg-blue-100",
                textColor: "text-blue-600",
              },
              {
                icon: Shield,
                title: "Privacy Protection",
                description: "Keep your personal information secure with our WHOIS privacy protection service.",
                color: "bg-green-100",
                textColor: "text-green-600",
              },
              {
                icon: Lock,
                title: "Domain Security",
                description: "Advanced security features to protect your domain from unauthorized transfers.",
                color: "bg-purple-100",
                textColor: "text-purple-600",
              },
              {
                icon: Server,
                title: "DNS Management",
                description: "Easy-to-use DNS management tools to configure your domain settings.",
                color: "bg-orange-100",
                textColor: "text-orange-600",
              },
              {
                icon: RefreshCw,
                title: "Auto-Renewal",
                description: "Never lose your domain with our automatic renewal service.",
                color: "bg-pink-100",
                textColor: "text-pink-600",
              },
              {
                icon: Zap,
                title: "Instant Setup",
                description: "Get your domain up and running quickly with our instant setup process.",
                color: "bg-indigo-100",
                textColor: "text-indigo-600",
              },
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full shadow-md hover:shadow-lg transition-shadow">
                  <CardBody className="p-6">
                    <div className={`${feature.color} w-14 h-14 rounded-full flex items-center justify-center mb-4`}>
                      <feature.icon className={`h-7 w-7 ${feature.textColor}`} />
                    </div>
                    <Typography variant="h5" className="font-bold mb-2">
                      {feature.title}
                    </Typography>
                    <Typography className="text-gray-600">
                      {feature.description}
                    </Typography>
                  </CardBody>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Premium Domains Section */}
      <section className="py-16 px-4 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <Typography variant="h2" className="text-3xl font-bold text-gray-900 mb-4">
              Premium Domain Services
            </Typography>
            <Typography className="text-lg text-gray-600 max-w-3xl mx-auto">
              Elevate your online presence with our premium domain services and features.
            </Typography>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white rounded-xl shadow-lg p-8"
            >
              <div className="flex items-start mb-6">
                <div className="bg-blue-100 p-3 rounded-full mr-4">
                  <Award className="h-8 w-8 text-blue-600" />
                </div>
                <div>
                  <Typography variant="h4" className="font-bold mb-2">
                    Premium Domain Acquisition
                  </Typography>
                  <Typography className="text-gray-600">
                    Our domain experts can help you acquire premium domains that are already owned. 
                    We negotiate on your behalf to secure the best possible price.
                  </Typography>
                </div>
              </div>
              <ul className="space-y-3">
                {[
                  "Access to exclusive premium domain inventory",
                  "Professional negotiation services",
                  "Secure domain transfer process",
                  "Escrow services for high-value domains",
                ].map((item, index) => (
                  <li key={index} className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-2" />
                    <Typography className="text-gray-700">{item}</Typography>
                  </li>
                ))}
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white rounded-xl shadow-lg p-8"
            >
              <div className="flex items-start mb-6">
                <div className="bg-purple-100 p-3 rounded-full mr-4">
                  <Clock className="h-8 w-8 text-purple-600" />
                </div>
                <div>
                  <Typography variant="h4" className="font-bold mb-2">
                    Domain Portfolio Management
                  </Typography>
                  <Typography className="text-gray-600">
                    Manage multiple domains with ease using our comprehensive domain portfolio management tools.
                  </Typography>
                </div>
              </div>
              <ul className="space-y-3">
                {[
                  "Centralized dashboard for all your domains",
                  "Bulk renewal and management options",
                  "Domain analytics and performance tracking",
                  "Automated expiration alerts and reminders",
                ].map((item, index) => (
                  <li key={index} className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-2" />
                    <Typography className="text-gray-700">{item}</Typography>
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 bg-blue-600 text-white">
        <div className="max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <Typography variant="h2" className="text-3xl md:text-4xl font-bold mb-4">
              Ready to Secure Your Domain?
            </Typography>
            <Typography className="text-xl text-blue-100 max-w-3xl mx-auto mb-8">
              Start building your online presence today with a domain name that represents your brand.
            </Typography>
            <Button
              size="lg"
              className="bg-white text-blue-600 hover:bg-blue-50"
              onClick={() => {
                // Scroll back to the search section
                window.scrollTo({ top: 0, behavior: 'smooth' });
              }}
            >
              Search for a Domain
            </Button>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
