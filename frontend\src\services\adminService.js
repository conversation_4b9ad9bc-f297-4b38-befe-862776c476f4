import apiService from '../app/lib/apiService';

export const adminService = {
  // Get dashboard statistics
  getDashboardStats: async () => {
    return await apiService.get('/admin/dashboard/stats');
  },
  
  // Get all users with pagination
  getUsers: async (page = 1, limit = 10, search = '') => {
    return await apiService.get(`/admin/users?page=${page}&limit=${limit}&search=${search}`);
  },
  
  // Add a new user
  addUser: async (userData) => {
    return await apiService.post('/admin/users', userData);
  },
  
  // Update a user
  updateUser: async (userId, userData) => {
    return await apiService.put(`/admin/users/${userId}`, userData);
  },
  
  // Delete a user
  deleteUser: async (userId) => {
    return await apiService.delete(`/admin/users/${userId}`);
  },
  
  // Get all packages
  getPackages: async () => {
    return await apiService.get('/admin/packages');
  },
  
  // Add a new package
  addPackage: async (packageData) => {
    return await apiService.post('/admin/packages', packageData);
  },
  
  // Update a package
  updatePackage: async (packageId, packageData) => {
    return await apiService.put(`/admin/packages/${packageId}`, packageData);
  },
  
  // Delete a package
  deletePackage: async (packageId) => {
    return await apiService.delete(`/admin/packages/${packageId}`);
  },
  
  // Get all orders with pagination
  getOrders: async (page = 1, limit = 10, status = '') => {
    return await apiService.get(`/admin/orders?page=${page}&limit=${limit}&status=${status}`);
  },
  
  // Update order status
  updateOrderStatus: async (orderId, status) => {
    return await apiService.put(`/admin/orders/${orderId}/status`, { status });
  },
  
  // Get all categories
  getCategories: async () => {
    return await apiService.get('/admin/categories');
  },
  
  // Get all specifications
  getSpecifications: async () => {
    return await apiService.get('/admin/specs');
  },
  
  // Add a new specification
  addSpecification: async (specData) => {
    return await apiService.post('/admin/specs', specData);
  },
  
  // Update a specification
  updateSpecification: async (specData) => {
    return await apiService.put('/admin/update-spec', specData);
  },

  getPackageDistribution: async (categoryId) => {
    return await apiService.get(`/admin/dashboard/package-distribution?categoryId=${categoryId}`);
  }
};

export default adminService;

