"use client"

import { useState, useEffect, useRef } from "react";
import { Bell, Check, Clock, Info } from "lucide-react";

const Notifications = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const notificationRef = useRef(null);

  // Sample notifications data - replace with your actual data source
  const notifications = [
    { id: 1, title: "New message received", content: "You have a new message from support team", time: "5 minutes ago", read: false },
    { id: 2, title: "Order completed", content: "Your order #12345 has been processed", time: "2 hours ago", read: false },
    { id: 3, title: "System update", content: "The system will be updated on Friday at 2 AM", time: "1 day ago", read: true },
    { id: 4, title: "Welcome to ZTech", content: "Thank you for joining our platform", time: "3 days ago", read: true },
  ];

  // Set mounted state to avoid hydration issues
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Handle click outside to close dropdown
  useEffect(() => {
    if (!isMounted) return;

    const handleClickOutside = (event) => {
      if (notificationRef.current && !notificationRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, isMounted]);

  // Count unread notifications
  const unreadCount = notifications.filter(notification => !notification.read).length;

  // Toggle notification panel
  const toggleNotifications = () => {
    setIsOpen(!isOpen);
  };

  // If not mounted yet, render a simplified version to avoid hydration mismatch
  if (!isMounted) {
    return (
      <div className="relative">
        <button className="relative p-2 rounded-full hover:bg-gray-100">
          <Bell className="w-5 h-5 text-gray-600" />
        </button>
      </div>
    );
  }
  // Get icon based on notification type
  const getNotificationIcon = (type) => {
    switch(type) {
      case "success": return <Check className="w-4 h-4 text-green-500" />;
      case "warning": return <Clock className="w-4 h-4 text-amber-500" />;
      default: return <Info className="w-4 h-4 text-blue-500" />;
    }
  };

  return (
    <div className="relative" ref={notificationRef}>
      <button
        className="relative p-2 rounded-full hover:bg-gray-100 border border-gray-100"
        aria-label="Notifications"
        onClick={toggleNotifications}
      >
        <Bell className="w-5 h-5 text-gray-600 " />
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 flex size-2">
          <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-red-400 opacity-75"></span>
          <span className="relative inline-flex size-2 rounded-full bg-red-500"></span>
        </span>
        )}
      </button>

      {/* Notification dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-custom-heavy border border-gray-200 z-50 overflow-hidden">
          <div className="p-3 border-b border-gray-200 bg-gray-50">
            <div className="flex justify-between items-center">
              <h3 className="font-semibold text-gray-700">Notifications</h3>
              {unreadCount > 0 && (
                <span className="px-2 py-0.5 bg-primary/10 text-primary text-xs font-medium rounded-full">
                  {unreadCount} new
                </span>
              )}
            </div>
          </div>

          <div className="max-h-96 overflow-y-auto hide-scrollbar">
            {notifications.length > 0 ? (
              <div className="divide-y divide-gray-100">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-3 hover:bg-gray-50 transition-colors cursor-pointer ${
                      !notification.read ? 'bg-blue-50/50' : ''
                    }`}
                  >
                    <div className="flex gap-3">
                      <div className="flex-shrink-0 mt-0.5">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          !notification.read ? 'bg-primary/10' : 'bg-gray-100'
                        }`}>
                          {getNotificationIcon(notification.type)}
                        </div>
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-start mb-1">
                          <h4 className={`text-sm font-medium truncate ${
                            !notification.read ? 'text-primary' : 'text-gray-800'
                          }`}>
                            {notification.title}
                          </h4>
                          <span className="text-xs text-gray-500 whitespace-nowrap ml-2">
                            {notification.time}
                          </span>
                        </div>
                        <p className="text-xs text-gray-600">{notification.content}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="py-8 px-4 text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-3">
                  <Bell className="w-6 h-6 text-gray-400" />
                </div>
                <p className="text-gray-500 mb-1">No notifications yet</p>
                <p className="text-sm text-gray-400">We{"'"}ll notify you when something arrives</p>
              </div>
            )}
          </div>

          <div className="p-2 border-t border-gray-200 bg-gray-50">
            <div className="flex justify-between items-center">
              <button className="text-sm text-primary hover:text-blue-700 font-medium transition-colors py-1">
                Mark all as read
              </button>
              <button className="text-sm text-gray-500 hover:text-gray-700 transition-colors py-1">
                View all
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Notifications;
