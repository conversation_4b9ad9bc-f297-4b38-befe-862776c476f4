"use client";
import React from "react";
import { usePathname } from "next/navigation";
import {
  FaFacebook,
  FaInstagram,
  FaLinkedin,
  FaPhoneAlt,
  FaTiktok,
  FaYoutube,
} from "react-icons/fa";
import LocalSwitcher from "../local-switcher";
import Link from "next/link";

export default function TopNavbar({ t }) {
  const path = usePathname();
  const normalizedPath = path.replace(/^\/[a-z]{2}/, "") || "/";

  return (
    <div className="bg-secondary h-[30px] w-full border-none text-white mx-auto p-2 md:px-4 md:py-1 flex items-center justify-between">
      <nav className="hidden lg:flex space-x-6 text-xs font-medium uppercase">
        {[
          { title: "Blog", url: "/blog" },
          { title: "Guide", url: "/guide" },
          // { title: t('promotion'), url: "/landing/demandez-devis" },
          { title: t("about_us"), url: "/about-us" },
          { title: t("contact"), url: "/about-us#contact-nous" },
          // { title: t('quote'), url: "/landing/demandez-devis-b" }
        ].map((item) => (
          <Link
            key={item.url}
            aria-label={`Go to ${item.title}`}
            href={item.url}
            className={`hover:text-black ${
              normalizedPath === item.url ? "text-black font-bold" : ""
            }`}
          >
            {item.title}
          </Link>
        ))}
      </nav>
      <div className="flex items-center justify-between md:justify-normal w-full md:w-fit md:space-x-4">
        <div className="lg:hidden xl:flex flex items-center space-x-1">
          <FaPhoneAlt />
          <span className="text-sm">+212 662 841 605</span>
        </div>
        <LocalSwitcher />
        <Link
          href="https://web.facebook.com/profile.php?id=61551999353576&_rdc=1&_rdr"
          aria-label="facebook"
          className="text-white"
        >
          <FaFacebook />
        </Link>
        <Link
          href="https://www.instagram.com/ztechengineering"
          aria-label="instagram"
          className="text-white"
        >
          <FaInstagram />
        </Link>
        <Link
          href="https://www.tiktok.com/@ztechengineering?is_from_webapp=1&sender_device=pc"
          aria-label="tiktok"
          className="text-white"
        >
          <FaTiktok />
        </Link>
        <Link
          href="https://www.linkedin.com/company/ztechengineering"
          aria-label="linkedin"
          className="text-white"
        >
          <FaLinkedin />
        </Link>
        <Link
          href="https://www.youtube.com/@ztechengineering/"
          aria-label="youtube"
          className="text-white"
        >
          <FaYoutube />
        </Link>
      </div>
    </div>
  );
}
