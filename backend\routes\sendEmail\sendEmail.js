const nodeMailer = require("nodemailer");
const { getTranslation } = require("../../helpers/helpers");

require("dotenv").config();

const {
  getVerificationAccountTemplate,
  getForgotPasswordTemplate,
  getBillingSummary,
  getPasswordResetSuccessfully,
  getReviewNotificationTemplate,
  getNewOrderAdminAlertTemp,
  getOrderSummaryTemplate,
  getTicketResolvedTemplate,
  getTicketCreatedTemplate,
  getSSLCertificateActivationAdminTemplate,
} = require("./emailTemplates");

const site_name = "ZtechEngineering";
const site_url = "https://ztechengineering.com";

const transporter = nodeMailer.createTransport({
  // service: "SMTP",
  host: process.env.MAIL_HOST,
  port: parseInt(process.env.MAIL_PORT, 10),
  secure: false, // Use false for TLS (STARTTLS)
  auth: {
    user: process.env.EMAIL_INFO_USER,
    pass: process.env.EMAIL_INFO_PASSWORD,
  },
  tls: {
    rejectUnauthorized: false, // Allow self-signed certificates (Not recommended for production)
  },
});

exports.verifyNodeMailer = async () => {
  try {
    const res = await transporter.verify();
    console.log("Server is ready to take our messages", res);
  } catch (error) {
    console.log("transporter.verify()  ...");
    console.log("error :", error);
  }
};

// The following emails are for the user management

exports.sendVerificationEmail = (req, email, uniqueString, user_id) => {
  const hostName = req.protocol + "://" + req.get("host");

  return new Promise((resolve, reject) => {
    const account_activation_link =
      hostName +
        "/auth/verify-email-account?activationField=" +
        uniqueString +
        "&userId=" +
        user_id +
        "&locale=" +
        req.cookies.BACKEND_LANG || "en";

    const mailOptions = {
      from: process.env.EMAIL_INFO_USER,
      to: email,
      subject: req.t("verifcation_account_subject"),
      html: getVerificationAccountTemplate(
        req,
        site_name,
        account_activation_link,
        site_url
      ),
    };

    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        reject(error);
      } else {
        resolve(info);
      }
    });
  });
};

exports.sendForgotPassword = (req, email, uniqueString, user_id) => {
  const hostName = req.protocol + "://" + req.get("host");
  return new Promise((resolve, reject) => {
    const account_activation_link =
      hostName +
        "/auth/verifyEmailAccountToResetPassword?activationField=" +
        uniqueString +
        "&userId=" +
        user_id +
        "&locale=" +
        req.cookies.BACKEND_LANG || "en";

    const mailOptions = {
      from: process.env.EMAIL_INFO_USER,
      to: email,
      subject: req.t("reset_password"),
      html: getForgotPasswordTemplate(
        req,
        site_name,
        account_activation_link,
        site_url
      ),
    };

    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        reject(error);
      } else {
        resolve(info);
      }
    });
  });
};

exports.sendSubscribeNewsletter = (req, email) => {
  const hostName = req.protocol + "://" + req.get("host");
  const dir = req.locale == "ar" ? "rtl" : "ltr";
  const htmlContent = `
  <div dir="${dir}">
    <p >${req.t("subscribe_email_content")}</p>
  <p>${req.t("to_unsubscribe_click_on")}
    <a href="${process.env.FRONTEND_URL}/${
    req.locale
  }?unsubscribeSomeOneInNewsletter=true&email=${email}" >
    ${req.t("unsubscribe_here")}
    </a>.
  </p>
  <br/>
  <div style="width:100%;display:flex;justify-content:center;margin-top:1rem">
    <a href="${process.env.FRONTEND_URL}/${
    req.locale
  }?unsubscribeSomeOneInNewsletter=true&email=${email}"  style="display: inline-block; text-decoration: none; padding: 6px 60px; font-size: 16px; color: #ffffff; background-color: #54B175; border-radius: 5px;">
  ${req.t("unsubscribe_here")}
  </a>
  </div>
  </div>


`;
  return new Promise((resolve, reject) => {
    const mailOptions = {
      from: process.env.MAIL_INFO_USER,
      to: email,
      subject: "subscribe Odhia",
      html: htmlContent,
    };

    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        console.log(error);

        reject(error);
      } else {
        resolve(info);
      }
    });
  });
};

exports.sendOrderSummaryEmail = (orderData) => {
  const lang = orderData.user.favoriteLang;
  const mailOptions = {
    from: process.env.EMAIL_INFO_USER,
    to: orderData.user.email,
    subject: getTranslation("subject_order_summary_ztech", lang),
    html: getOrderSummaryTemplate(orderData, lang),
  };

  return new Promise((resolve, reject) => {
    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        reject(error);
      } else {
        resolve(info);
      }
    });
  });
};

exports.sendNewOrderAdminAlert = (orderData) => {
  const mailOptions = {
    from: process.env.EMAIL_INFO_USER,
    to: process.env.EMAIL_ADMIN_USER,
    subject: "New Order Alert from ZTechEngineering",
    html: getNewOrderAdminAlertTemp(orderData, "en"),
  };

  return new Promise((resolve, reject) => {
    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        reject(error);
      } else {
        resolve(info);
      }
    });
  });
};

exports.sendBillingReceiptEmail = (orderData) => {
  const mailOptions = {
    from: process.env.MAIL_INFO_USER,
    to: orderData.billing.email,
    // subject: 'Transaction Successful! Payment Details Inside',
    subject: getTranslation(
      "subject_transaction_successful",
      orderData.customer.favoriteLang
    ),
    html: getBillingSummary(orderData),
  };

  return new Promise((resolve, reject) => {
    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        reject(error);
      } else {
        resolve(info);
      }
    });
  });
};

//to be implemented, when the user successfully resets their password.. we should execute this function
exports.sendYourPwResetedSuccess = (userData) => {
  const mailOptions = {
    from: process.env.MAIL_NOREPLY_USER,
    to: userData.email,
    subject: "You Password has been updated",
    html: getPasswordResetSuccessfully(userData),
  };

  return new Promise((resolve, reject) => {
    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        reject(error);
      } else {
        resolve(info);
      }
    });
  });
};

exports.sendNewReviewReceived = (reviewData) => {
  const mailOptions = {
    from: process.env.MAIL_NOREPLY_USER,
    to: reviewData.reviewedUser.email,
    subject: getTranslation(
      "new_rating_notification_title",
      reviewData.reviewedUser.favoriteLang
    ),
    html: getReviewNotificationTemplate(reviewData),
  };

  return new Promise((resolve, reject) => {
    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        reject(error);
      } else {
        resolve(info);
      }
    });
  });
};

exports.sendEmail = (data, emailTemplate) => {
  const mailOptions = {
    from: process.env.EMAIL_INFO_USER,
    to: data.mailto,
    subject: getTranslation(data.subject, data.favoriteLang),
    html: emailTemplate,
  };

  return new Promise((resolve, reject) => {
    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        reject(error);
      } else {
        resolve(info);
      }
    });
  });
};

// Function to send email notifications for suborder status updates
exports.sendSubOrderStatusUpdateEmail = (
  suborder,
  user,
  subOrderEmailTemplate
) => {
  const mailOptions = {
    from: process.env.EMAIL_ADMIN_USER,
    to: user.email, // Send to the customer's email
    subject: "Suborder Status Update",
    html: subOrderEmailTemplate(
      suborder,
      user,
      user.favoriteLang === "en" ? "en-US" : "fr-fr"
    ),
  };

  return new Promise((resolve, reject) => {
    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        reject(error);
      } else {
        resolve(info);
      }
    });
  });
};

// Function to send email notifications for SSL certificate status updates
exports.sendSSLCertificateStatusUpdateEmail = (
  certificate,
  suborder,
  user,
  sslCertificateEmailTemplate
) => {
  const mailOptions = {
    from: process.env.EMAIL_ADMIN_USER,
    to: user.email, // Send to the customer's email
    subject: "SSL Certificate Status Update",
    html: sslCertificateEmailTemplate(
      certificate,
      suborder,
      user,
      user.favoriteLang === "en" ? "en-US" : "fr-fr"
    ),
  };

  return new Promise((resolve, reject) => {
    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        reject(error);
      } else {
        resolve(info);
      }
    });
  });
};

exports.sendTicketMail = async (state, data) => {
  // Ensure the state is either "created" or "resolved"
  if (!["created", "resolved"].includes(state)) {
    throw new Error('Invalid state. State must be "created" or "resolved".');
  }

  let subject, templateFunction, recipient;

  // Determine the subject, template, and recipient based on the state
  if (state === "created") {
    subject = "New Ticket Created";
    templateFunction = getTicketCreatedTemplate(
      data,
      data.creator.favoriteLang
    ); // Path to your getTicketCreatedTemplate function
    recipient = process.env.EMAIL_ADMIN_USER; // Send to admin when ticket is created
  } else if (state === "resolved") {
    subject = "Your Ticket Has Been Resolved";
    templateFunction = getTicketResolvedTemplate(
      data,
      data.creator.favoriteLang
    ); // Path to your getTicketResolvedTemplate function
    recipient = data.creator.email; // Send to the ticket creator when resolved
  }

  // Create the mail options
  let mailOptions = {
    from: process.env.EMAIL_INFO_USER,
    to: recipient,
    subject: subject,
    html: templateFunction, // Use the appropriate template function
  };

  return new Promise((resolve, reject) => {
    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        reject(error);
      } else {
        resolve(info);
      }
    });
  });
};

// Function to send email notifications to admin when SSL certificate is activated
exports.sendSSLCertificateActivationAdminAlert = (
  certificate,
  suborder,
  user
) => {
  const adminEmail = process.env.EMAIL_ADMIN_USER ; // Use provided admin email as fallback
  console.log("adminEmail: ", adminEmail);
  const mailOptions = {
    from: process.env.EMAIL_INFO_USER,
    to: adminEmail,
    subject: "New SSL Certificate Activation",
    html: getSSLCertificateActivationAdminTemplate(
      certificate,
      suborder,
      user
    ),
  };

  return new Promise((resolve, reject) => {
    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        console.error("Failed to send SSL certificate activation admin alert:", error);
        reject(error);
      } else {
        console.log(`SSL certificate activation admin alert sent to ${adminEmail}`);
        resolve(info);
      }
    });
  });
};
