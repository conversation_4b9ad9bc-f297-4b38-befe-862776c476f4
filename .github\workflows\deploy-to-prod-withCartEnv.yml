name: Deploy ztechEngineering to PROD WITH CART Environment

on:
  push:
    branches: [prodwithcart]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'

      - name: Install frontend modules
        run: |
          cd frontend
          npm ci
          npm list # Debug: Show installed dependencies

      - name: Build the frontend
        run: |
          cd frontend
          npm run build
          sync # Ensure file writes are complete
          ls -la .next # Debug: Verify build output

      - name: Install backend modules
        run: |
          cd backend
          npm ci
          npm list # Debug: Show installed dependencies

      - name: Create frontend deployment package
        run: |
          cd frontend
          tar -czf ../frontend.tar.gz .next package.json package-lock.json
          cd ..
          ls -lh frontend.tar.gz # Debug: Verify package size

      - name: Verify frontend deployment package
        run: |
          if [ ! -f frontend.tar.gz ]; then
            echo "ERROR: frontend.tar.gz was not created!"
            exit 1
          fi

      - name: Debug backend files before packaging
        run: |
          cd backend
          ls -la # Debug: List files before creating tar
          cd ..

      - name: Create backend deployment package
        run: |
          cd backend
          tar -czf ../backend.tar.gz --exclude='node_modules' .
          cd ..
          ls -lh backend.tar.gz # Debug: Verify package size

      - name: Verify backend deployment package
        run: |
          if [ ! -f backend.tar.gz ]; then
            echo "ERROR: backend.tar.gz was not created!"
            exit 1
          fi

      - name: Upload frontend deployment package
        uses: actions/upload-artifact@v4
        with:
          name: frontend-package
          path: frontend.tar.gz
          retention-days: 1

      - name: Upload backend deployment package
        uses: actions/upload-artifact@v4
        with:
          name: backend-package
          path: backend.tar.gz
          retention-days: 1

  deploy:
    runs-on: ["self-hosted", "ztech-nextjs"]
    needs: build
    steps:
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          # cache: 'npm'
          # cache-dependency-path: '**/package-lock.json'

      - name: Clean frontend directory
        run: |
          rm -rf frontend/.next frontend/package.json frontend/package-lock.json
          ls -la frontend # Debug: Verify clean state

      - name: Download frontend deployment package
        uses: actions/download-artifact@v4
        with:
          name: frontend-package
          path: .

      - name: Verify frontend package download
        run: |
          ls -lh frontend.tar.gz # Debug: Verify download
          if [ ! -f frontend.tar.gz ]; then
            echo "ERROR: frontend.tar.gz not found!"
            exit 1
          fi

      - name: Extract frontend deployment package
        run: |
          tar -xzf frontend.tar.gz -C frontend
          rm frontend.tar.gz
          ls -la frontend # Debug: Verify extracted files

      - name: Clean backend directory
        run: |
          rm -rf backend/*
          ls -la backend # Debug: Verify clean state

      - name: Download backend deployment package
        uses: actions/download-artifact@v4
        with:
          name: backend-package
          path: .

      - name: Verify backend package download
        run: |
          ls -lh backend.tar.gz # Debug: Verify download
          if [ ! -f backend.tar.gz ]; then
            echo "ERROR: backend.tar.gz not found!"
            exit 1
          fi

      - name: Extract backend deployment package
        run: |
          tar -xzf backend.tar.gz -C backend
          rm backend.tar.gz
          ls -la backend # Debug: Verify extracted files

      - name: Write .env for backend
        run: |
          cd backend
          echo "${{ secrets.PROD_ENV_FILE_CONTENT_08_05_2025}}" > .env
          chmod 600 .env # Secure permissions
          ls -la .env # Debug: Verify .env file

      - name: Install backend dependencies
        run: |
          cd backend
          npm ci
          npm list # Debug: Show installed dependencies

      - name: Start frontend with pm2 (port 3001)
        run: |
          cd frontend
          pm2 delete ztech-frontend || true
          pm2 start npm --name ztech-frontend -- start -- --port=3001
          pm2 list # Debug: Show pm2 processes

      - name: Start backend with pm2 (port 5002)
        run: |
          cd backend
          pm2 delete ztech-backend || true
          pm2 start npm --name ztech-backend -- start -- --port=5002
          pm2 save
          pm2 list # Debug: Show pm2 processes