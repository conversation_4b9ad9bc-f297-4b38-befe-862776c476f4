# Domain Registration Setup

This document explains how to set up the domain registration system with your reseller account.

## Overview

The domain registration system supports two approaches:

1. **Company Account Approach** (Recommended): Use a single internal customer account for all domain registrations
2. **Individual Account Approach**: Create a separate customer account in the reseller system for each user

## Environment Variables

To use the company account approach, you need to set the following environment variables:

```
# Company account for domain registrations
COMPANY_CUSTOMER_ID=your_customer_id
COMPANY_REG_CONTACT_ID=your_reg_contact_id
COMPANY_ADMIN_CONTACT_ID=your_admin_contact_id
COMPANY_TECH_CONTACT_ID=your_tech_contact_id
COMPANY_BILLING_CONTACT_ID=your_billing_contact_id
```

## Setting Up the Company Account

1. Log in to your reseller control panel
2. Create a new customer account for your company
3. Create contact profiles for the company (registrant, admin, technical, and billing)
4. Note the IDs for the customer and all contacts
5. Add these IDs to your environment variables

## API Endpoints

### Customer Signup

```
POST /domainMng/customer-signup
```

**Request Body:**
```json
{
  "username": "<EMAIL>",
  "passwd": "password",
  "name": "Customer Name",
  "company": "Company Name",
  "addressLine1": "123 Main St",
  "city": "City",
  "state": "State",
  "country": "US",
  "zipcode": "12345",
  "phoneCc": "1",
  "phone": "**********",
  "langPref": "en",
  "userId": "optional_user_id_from_your_system"
}
```

### Domain Registration

```
POST /domainMng/register-domain
```

**Request Body:**
```json
{
  "domain": "example.com",
  "years": 1,
  "ns1": "ns1.domain-name-api.dynv6.net",
  "ns2": "ns2.domain-name-api.dynv6.net",
  "autoRenew": false,
  "privacyProtection": false,
  "useCompanyAccount": true
}
```

If `useCompanyAccount` is set to `false`, you must also provide:

```json
{
  "customerId": "customer_id",
  "regContactId": "registrant_contact_id",
  "adminContactId": "admin_contact_id",
  "techContactId": "technical_contact_id",
  "billingContactId": "billing_contact_id"
}
```

## Handling Email Verification

When using the company account approach, no verification emails will be sent to your customers since all domains are registered under your company's account.

If you choose to use individual customer accounts, be aware that the reseller API will automatically send verification emails to new customers. You may want to inform your users about this or consider using the company account approach to avoid confusion.
