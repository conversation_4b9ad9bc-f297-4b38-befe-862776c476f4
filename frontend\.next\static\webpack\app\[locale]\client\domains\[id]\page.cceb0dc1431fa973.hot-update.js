"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/components/domains/DomainRegistrationStatus.jsx":
/*!*************************************************************!*\
  !*** ./src/components/domains/DomainRegistrationStatus.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\n * Component to display domain registration status and allow manual registration\n */ const DomainRegistrationStatus = (param)=>{\n    let { domain, onRegistrationComplete } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)(\"client.domainWrapper\");\n    const handleRegisterDomain = async ()=>{\n        if (!domain.orderId) {\n            setError(\"Order ID is missing\");\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].registerDomainsForOrder(domain.orderId);\n            if (response.data.success) {\n                setSuccess(\"Domain registration initiated successfully\");\n                if (onRegistrationComplete) {\n                    onRegistrationComplete(response.data);\n                }\n            } else {\n                setError(response.data.message || \"Failed to register domain\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error registering domain:\", error);\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"An error occurred while registering the domain\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Determine if registration is needed based on domain status\n    const registrationNeeded = domain.status === \"pending\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-4\",\n        children: [\n            domain.status === \"active\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"green\",\n                className: \"mb-4\",\n                children: t(\"domain_active_message\", \"Your domain is active and ready to use.\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, undefined) : domain.status === \"pending\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"yellow\",\n                className: \"mb-4\",\n                children: t(\"domain_pending_message\", \"Your domain registration is pending. Click the button below to complete registration.\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"gray\",\n                className: \"mb-4\",\n                children: t(\"domain_status_message\", \"Domain status: \".concat(domain.status))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"red\",\n                className: \"mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, undefined),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"green\",\n                className: \"mb-4\",\n                children: success\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, undefined),\n            registrationNeeded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                className: \"bg-blue-600 hover:bg-blue-700\",\n                onClick: handleRegisterDomain,\n                disabled: loading,\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-4 w-4 mr-2 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                            lineNumber: 95,\n                            columnNumber: 15\n                        }, undefined),\n                        t(\"registering_domain\", \"Registering Domain...\")\n                    ]\n                }, void 0, true) : t(\"complete_domain_registration\", \"Complete Domain Registration\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainRegistrationStatus, \"Xs4gaPaxqLPAOtbp1Zjj30AFfUo=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations\n    ];\n});\n_c = DomainRegistrationStatus;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainRegistrationStatus);\nvar _c;\n$RefreshReg$(_c, \"DomainRegistrationStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/DomainRegistrationStatus.jsx\n"));

/***/ })

});