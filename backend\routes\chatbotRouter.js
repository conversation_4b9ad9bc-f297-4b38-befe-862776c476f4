const express = require('express');
const chatbotRouter = express.Router();
const chatbotController = require('../controllers/chatbotController');
const { checkUserOrRefreshToken } = require('../midelwares/authorization');
const { asyncBackFrontEndLang } = require('../midelwares/sharedMidd');

// Add a test route to verify the router is working
chatbotRouter.get('/test', (req, res) => {
  console.log('[CHATBOT DEBUG] Test route accessed');
  res.json({ message: 'Chatbot router is working' });
});

// Process incoming chat messages - apply middleware to identify user or guest
chatbotRouter.post('/', asyncBackFrontEndLang, checkUserOrRefreshToken, chatbotController.processMessage);

// Clear a chat session
chatbotRouter.delete('/session/:sessionId', asyncBackFrontEndLang, checkUserOrRefreshToken, chatbotController.clearSession);

console.log('[CHATBOT DEBUG] Chatbot router setup complete');

module.exports = chatbotRouter;