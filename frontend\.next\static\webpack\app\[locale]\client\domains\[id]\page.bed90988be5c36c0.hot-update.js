"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/components/domains/DomainRegistrationStatus.jsx":
/*!*************************************************************!*\
  !*** ./src/components/domains/DomainRegistrationStatus.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\n * Component to display domain registration status and allow manual registration\n */ const DomainRegistrationStatus = (param)=>{\n    let { domain, onRegistrationComplete } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)(\"client\");\n    const handleRegisterDomain = async ()=>{\n        if (!domain.orderId) {\n            setError(\"Order ID is missing\");\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].registerDomainsForOrder(domain.orderId);\n            if (response.data.success) {\n                setSuccess(\"Domain registration initiated successfully\");\n                if (onRegistrationComplete) {\n                    onRegistrationComplete(response.data);\n                }\n            } else {\n                setError(response.data.message || \"Failed to register domain\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error registering domain:\", error);\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"An error occurred while registering the domain\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Determine if registration is needed based on domain status\n    const registrationNeeded = domain.status === \"pending\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-4\",\n        children: [\n            domain.status === \"active\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"green\",\n                className: \"mb-4\",\n                children: t(\"domain_active_message\", \"Your domain is active and ready to use.\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, undefined) : domain.status === \"pending\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"yellow\",\n                className: \"mb-4\",\n                children: t(\"domain_pending_message\", \"Your domain registration is pending. Click the button below to complete registration.\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"gray\",\n                className: \"mb-4\",\n                children: t(\"domain_status_message\", \"Domain status: \".concat(domain.status))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"red\",\n                className: \"mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, undefined),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"green\",\n                className: \"mb-4\",\n                children: success\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, undefined),\n            registrationNeeded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                className: \"bg-blue-600 hover:bg-blue-700\",\n                onClick: handleRegisterDomain,\n                disabled: loading,\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-4 w-4 mr-2 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                            lineNumber: 95,\n                            columnNumber: 15\n                        }, undefined),\n                        t(\"registering_domain\", \"Registering Domain...\")\n                    ]\n                }, void 0, true) : t(\"complete_domain_registration\", \"Complete Domain Registration\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainRegistrationStatus, \"Xs4gaPaxqLPAOtbp1Zjj30AFfUo=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations\n    ];\n});\n_c = DomainRegistrationStatus;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainRegistrationStatus);\nvar _c;\n$RefreshReg$(_c, \"DomainRegistrationStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2RvbWFpbnMvRG9tYWluUmVnaXN0cmF0aW9uU3RhdHVzLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBd0M7QUFDNkI7QUFDOUI7QUFDd0I7QUFDbkI7QUFFNUM7O0NBRUMsR0FDRCxNQUFNUSwyQkFBMkI7UUFBQyxFQUFFQyxNQUFNLEVBQUVDLHNCQUFzQixFQUFFOztJQUNsRSxNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR1gsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDWSxPQUFPQyxTQUFTLEdBQUdiLCtDQUFRQSxDQUFDO0lBQ25DLE1BQU0sQ0FBQ2MsU0FBU0MsV0FBVyxHQUFHZiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNZ0IsSUFBSVYsMERBQWVBLENBQUM7SUFFMUIsTUFBTVcsdUJBQXVCO1FBQzNCLElBQUksQ0FBQ1QsT0FBT1UsT0FBTyxFQUFFO1lBQ25CTCxTQUFTO1lBQ1Q7UUFDRjtRQUVBRixXQUFXO1FBQ1hFLFNBQVM7UUFDVEUsV0FBVztRQUVYLElBQUk7WUFDRixNQUFNSSxXQUFXLE1BQU1kLHNFQUFnQkEsQ0FBQ2UsdUJBQXVCLENBQzdEWixPQUFPVSxPQUFPO1lBR2hCLElBQUlDLFNBQVNFLElBQUksQ0FBQ1AsT0FBTyxFQUFFO2dCQUN6QkMsV0FBVztnQkFDWCxJQUFJTix3QkFBd0I7b0JBQzFCQSx1QkFBdUJVLFNBQVNFLElBQUk7Z0JBQ3RDO1lBQ0YsT0FBTztnQkFDTFIsU0FBU00sU0FBU0UsSUFBSSxDQUFDQyxPQUFPLElBQUk7WUFDcEM7UUFDRixFQUFFLE9BQU9WLE9BQU87Z0JBR1pBLHNCQUFBQTtZQUZGVyxRQUFRWCxLQUFLLENBQUMsNkJBQTZCQTtZQUMzQ0MsU0FDRUQsRUFBQUEsa0JBQUFBLE1BQU1PLFFBQVEsY0FBZFAsdUNBQUFBLHVCQUFBQSxnQkFBZ0JTLElBQUksY0FBcEJULDJDQUFBQSxxQkFBc0JVLE9BQU8sS0FDM0I7UUFFTixTQUFVO1lBQ1JYLFdBQVc7UUFDYjtJQUNGO0lBRUEsNkRBQTZEO0lBQzdELE1BQU1hLHFCQUFxQmhCLE9BQU9pQixNQUFNLEtBQUs7SUFFN0MscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7O1lBQ1puQixPQUFPaUIsTUFBTSxLQUFLLHlCQUNqQiw4REFBQ3RCLDJEQUFLQTtnQkFBQ3lCLE9BQU07Z0JBQVFELFdBQVU7MEJBQzVCWCxFQUNDLHlCQUNBOzs7Ozs0QkFHRlIsT0FBT2lCLE1BQU0sS0FBSywwQkFDcEIsOERBQUN0QiwyREFBS0E7Z0JBQUN5QixPQUFNO2dCQUFTRCxXQUFVOzBCQUM3QlgsRUFDQywwQkFDQTs7Ozs7MENBSUosOERBQUNiLDJEQUFLQTtnQkFBQ3lCLE9BQU07Z0JBQU9ELFdBQVU7MEJBQzNCWCxFQUFFLHlCQUF5QixrQkFBZ0MsT0FBZFIsT0FBT2lCLE1BQU07Ozs7OztZQUk5RGIsdUJBQ0MsOERBQUNULDJEQUFLQTtnQkFBQ3lCLE9BQU07Z0JBQU1ELFdBQVU7MEJBQzFCZjs7Ozs7O1lBSUpFLHlCQUNDLDhEQUFDWCwyREFBS0E7Z0JBQUN5QixPQUFNO2dCQUFRRCxXQUFVOzBCQUM1QmI7Ozs7OztZQUlKVSxvQ0FDQyw4REFBQ3ZCLDREQUFNQTtnQkFDTDBCLFdBQVU7Z0JBQ1ZFLFNBQVNaO2dCQUNUYSxVQUFVcEI7MEJBRVRBLHdCQUNDOztzQ0FDRSw4REFBQ04sbUZBQU9BOzRCQUFDdUIsV0FBVTs7Ozs7O3dCQUNsQlgsRUFBRSxzQkFBc0I7O21DQUczQkEsRUFBRSxnQ0FBZ0M7Ozs7Ozs7Ozs7OztBQU05QztHQS9GTVQ7O1FBSU1ELHNEQUFlQTs7O0tBSnJCQztBQWlHTiwrREFBZUEsd0JBQXdCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2RvbWFpbnMvRG9tYWluUmVnaXN0cmF0aW9uU3RhdHVzLmpzeD9lNTIwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgQnV0dG9uLCBUeXBvZ3JhcGh5LCBBbGVydCB9IGZyb20gXCJAbWF0ZXJpYWwtdGFpbHdpbmQvcmVhY3RcIjtcbmltcG9ydCB7IExvYWRlcjIgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XG5pbXBvcnQgZG9tYWluTW5nU2VydmljZSBmcm9tIFwiQC9hcHAvc2VydmljZXMvZG9tYWluTW5nU2VydmljZVwiO1xuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb25zIH0gZnJvbSBcIm5leHQtaW50bFwiO1xuXG4vKipcbiAqIENvbXBvbmVudCB0byBkaXNwbGF5IGRvbWFpbiByZWdpc3RyYXRpb24gc3RhdHVzIGFuZCBhbGxvdyBtYW51YWwgcmVnaXN0cmF0aW9uXG4gKi9cbmNvbnN0IERvbWFpblJlZ2lzdHJhdGlvblN0YXR1cyA9ICh7IGRvbWFpbiwgb25SZWdpc3RyYXRpb25Db21wbGV0ZSB9KSA9PiB7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW3N1Y2Nlc3MsIHNldFN1Y2Nlc3NdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IHQgPSB1c2VUcmFuc2xhdGlvbnMoXCJjbGllbnRcIik7XG5cbiAgY29uc3QgaGFuZGxlUmVnaXN0ZXJEb21haW4gPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFkb21haW4ub3JkZXJJZCkge1xuICAgICAgc2V0RXJyb3IoXCJPcmRlciBJRCBpcyBtaXNzaW5nXCIpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IobnVsbCk7XG4gICAgc2V0U3VjY2VzcyhudWxsKTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGRvbWFpbk1uZ1NlcnZpY2UucmVnaXN0ZXJEb21haW5zRm9yT3JkZXIoXG4gICAgICAgIGRvbWFpbi5vcmRlcklkXG4gICAgICApO1xuXG4gICAgICBpZiAocmVzcG9uc2UuZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHNldFN1Y2Nlc3MoXCJEb21haW4gcmVnaXN0cmF0aW9uIGluaXRpYXRlZCBzdWNjZXNzZnVsbHlcIik7XG4gICAgICAgIGlmIChvblJlZ2lzdHJhdGlvbkNvbXBsZXRlKSB7XG4gICAgICAgICAgb25SZWdpc3RyYXRpb25Db21wbGV0ZShyZXNwb25zZS5kYXRhKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0RXJyb3IocmVzcG9uc2UuZGF0YS5tZXNzYWdlIHx8IFwiRmFpbGVkIHRvIHJlZ2lzdGVyIGRvbWFpblwiKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHJlZ2lzdGVyaW5nIGRvbWFpbjpcIiwgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoXG4gICAgICAgIGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8XG4gICAgICAgICAgXCJBbiBlcnJvciBvY2N1cnJlZCB3aGlsZSByZWdpc3RlcmluZyB0aGUgZG9tYWluXCJcbiAgICAgICk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyBEZXRlcm1pbmUgaWYgcmVnaXN0cmF0aW9uIGlzIG5lZWRlZCBiYXNlZCBvbiBkb21haW4gc3RhdHVzXG4gIGNvbnN0IHJlZ2lzdHJhdGlvbk5lZWRlZCA9IGRvbWFpbi5zdGF0dXMgPT09IFwicGVuZGluZ1wiO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtdC00XCI+XG4gICAgICB7ZG9tYWluLnN0YXR1cyA9PT0gXCJhY3RpdmVcIiA/IChcbiAgICAgICAgPEFsZXJ0IGNvbG9yPVwiZ3JlZW5cIiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAge3QoXG4gICAgICAgICAgICBcImRvbWFpbl9hY3RpdmVfbWVzc2FnZVwiLFxuICAgICAgICAgICAgXCJZb3VyIGRvbWFpbiBpcyBhY3RpdmUgYW5kIHJlYWR5IHRvIHVzZS5cIlxuICAgICAgICAgICl9XG4gICAgICAgIDwvQWxlcnQ+XG4gICAgICApIDogZG9tYWluLnN0YXR1cyA9PT0gXCJwZW5kaW5nXCIgPyAoXG4gICAgICAgIDxBbGVydCBjb2xvcj1cInllbGxvd1wiIGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICB7dChcbiAgICAgICAgICAgIFwiZG9tYWluX3BlbmRpbmdfbWVzc2FnZVwiLFxuICAgICAgICAgICAgXCJZb3VyIGRvbWFpbiByZWdpc3RyYXRpb24gaXMgcGVuZGluZy4gQ2xpY2sgdGhlIGJ1dHRvbiBiZWxvdyB0byBjb21wbGV0ZSByZWdpc3RyYXRpb24uXCJcbiAgICAgICAgICApfVxuICAgICAgICA8L0FsZXJ0PlxuICAgICAgKSA6IChcbiAgICAgICAgPEFsZXJ0IGNvbG9yPVwiZ3JheVwiIGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICB7dChcImRvbWFpbl9zdGF0dXNfbWVzc2FnZVwiLCBgRG9tYWluIHN0YXR1czogJHtkb21haW4uc3RhdHVzfWApfVxuICAgICAgICA8L0FsZXJ0PlxuICAgICAgKX1cblxuICAgICAge2Vycm9yICYmIChcbiAgICAgICAgPEFsZXJ0IGNvbG9yPVwicmVkXCIgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgIHtlcnJvcn1cbiAgICAgICAgPC9BbGVydD5cbiAgICAgICl9XG5cbiAgICAgIHtzdWNjZXNzICYmIChcbiAgICAgICAgPEFsZXJ0IGNvbG9yPVwiZ3JlZW5cIiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAge3N1Y2Nlc3N9XG4gICAgICAgIDwvQWxlcnQ+XG4gICAgICApfVxuXG4gICAgICB7cmVnaXN0cmF0aW9uTmVlZGVkICYmIChcbiAgICAgICAgPEJ1dHRvblxuICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwXCJcbiAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVSZWdpc3RlckRvbWFpbn1cbiAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgPlxuICAgICAgICAgIHtsb2FkaW5nID8gKFxuICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yIGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICAgIHt0KFwicmVnaXN0ZXJpbmdfZG9tYWluXCIsIFwiUmVnaXN0ZXJpbmcgRG9tYWluLi4uXCIpfVxuICAgICAgICAgICAgPC8+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIHQoXCJjb21wbGV0ZV9kb21haW5fcmVnaXN0cmF0aW9uXCIsIFwiQ29tcGxldGUgRG9tYWluIFJlZ2lzdHJhdGlvblwiKVxuICAgICAgICAgICl9XG4gICAgICAgIDwvQnV0dG9uPlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IERvbWFpblJlZ2lzdHJhdGlvblN0YXR1cztcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiQnV0dG9uIiwiVHlwb2dyYXBoeSIsIkFsZXJ0IiwiTG9hZGVyMiIsImRvbWFpbk1uZ1NlcnZpY2UiLCJ1c2VUcmFuc2xhdGlvbnMiLCJEb21haW5SZWdpc3RyYXRpb25TdGF0dXMiLCJkb21haW4iLCJvblJlZ2lzdHJhdGlvbkNvbXBsZXRlIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwic3VjY2VzcyIsInNldFN1Y2Nlc3MiLCJ0IiwiaGFuZGxlUmVnaXN0ZXJEb21haW4iLCJvcmRlcklkIiwicmVzcG9uc2UiLCJyZWdpc3RlckRvbWFpbnNGb3JPcmRlciIsImRhdGEiLCJtZXNzYWdlIiwiY29uc29sZSIsInJlZ2lzdHJhdGlvbk5lZWRlZCIsInN0YXR1cyIsImRpdiIsImNsYXNzTmFtZSIsImNvbG9yIiwib25DbGljayIsImRpc2FibGVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/DomainRegistrationStatus.jsx\n"));

/***/ })

});