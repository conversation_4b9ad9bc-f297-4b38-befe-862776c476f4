"use client";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import {
  Typo<PERSON>,
  <PERSON>,
  CardBody,
  Button,
} from "@material-tailwind/react";
import "react-loading-skeleton/dist/skeleton.css";
import PricingPlanGrid from "@/components/hosting/pricingPlanGrid";
import { getFormattedPlans, getMaxDiscount } from "@/app/helpers/helpers";
import packageService from "@/app/services/packageService";
import Lottie from "lottie-react";
import SharedHosting from "src/assets/shared-hosting.json";
import { CheckCircleIcon, RocketIcon } from "lucide-react";

export default function SharedHostingPage() {
  const t = useTranslations("hosting");
  const [billingPeriod, setBillingPeriod] = useState("yearly");
  const [dedicatedHostingPacks, setDedicatedHostingPacks] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getPackagesData = async () => {
      try {
        const response = await packageService.getPackages("Shared hosting");
        setDedicatedHostingPacks(response.data);
      } catch (error) {
        console.error("error fetching promotions: ", error);
      } finally {
        setLoading(false);
      }
    };

    getPackagesData();
  }, []);

  const dedicatedPlans = getFormattedPlans(dedicatedHostingPacks, billingPeriod);
  const maxDiscount = getMaxDiscount(dedicatedHostingPacks);

  return (
    <div className="min-h-screen bg-gray-50 pt-4 pb-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Section */}
        <section>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div className="text-center lg:text-left">
              <Typography
                variant="h1"
                color="blue-gray"
                className="text-4xl lg:text-5xl font-inter font-bold mb-6 tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-[#0B2D6A] to-indigo-500"
              >
                {t("headline")}
              </Typography>
              <Typography
                variant="lead"
                color="gray"
                className="text-lg mb-8 font-light"
              >
                {t("all_in_one_solution")}
              </Typography>
              <Button
                color="blue"
                size="lg"
                className="md:hidden rounded-full flex gap-2 justify-center items-center w-full sm:w-auto"
                onClick={() => document.getElementById("pricing")?.scrollIntoView({ behavior: "smooth" })}
              >
                <RocketIcon className="w-6 h-6 mr-2" />
                {t("explore_plans")}
              </Button>
            </div>
            <div className="flex justify-center lg:justify-end">
              <Lottie
                animationData={SharedHosting}
                loop={true}
                className="w-[21rem] max-w-sm lg:max-w-md"
              />
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="mt-0 lg:mt-[-30px] mb-20">
          <PricingPlanGrid
            loading={loading}
            plans={dedicatedPlans}
            billingPeriod={billingPeriod}
            t={t}
            setBillingPeriod={setBillingPeriod}
            getMaxDiscount={() => maxDiscount}
          />
        </section>

        {/* Features Section */}
        <section className="py-16 bg-gray-50">
      {/* Section Title */}
      <div className="text-center mb-12">
        <Typography
          variant="h2"
          color="blue-gray"
          className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-4"
        >
          {t("features_title")}
        </Typography>
        <Typography
          variant="lead"
          color="gray"
          className="text-lg md:text-xl font-light text-gray-600 max-w-2xl mx-auto"
        >
          {t("features_description")}
        </Typography>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 px-4 sm:px-6 lg:px-8">
        {[
          {
            title: t("features.0.title"),
            description: t("features.0.description"),
            icon: <CheckCircleIcon className="h-8 w-8 text-green-700" />,
          },
          {
            title: t("features.1.title"),
            description: t("features.1.description"),
            icon: <CheckCircleIcon className="h-8 w-8 text-green-700" />,
          },
          {
            title: t("features.2.title"),
            description: t("features.2.description"),
            icon: <CheckCircleIcon className="h-8 w-8 text-green-700" />,
          },
        ].map((feature, index) => (
          <Card
            key={index}
            className="bg-white hover:shadow-2xl transition-shadow duration-300 rounded-2xl border border-gray-100 p-6 flex flex-col items-center text-center"
          >
            <div className="mb-6">
              {feature.icon}
            </div>
            <CardBody className="p-0">
              <Typography
                variant="h5"
                color="blue-gray"
                className="text-xl md:text-2xl font-semibold mb-3"
              >
                {feature.title}
              </Typography>
              <Typography
                color="gray"
                className="text-sm md:text-base font-normal text-gray-600"
              >
                {feature.description}
              </Typography>
            </CardBody>
          </Card>
        ))}
      </div>
    </section>
      </div>
    </div>
  );
}