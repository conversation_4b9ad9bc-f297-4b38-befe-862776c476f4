"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx":
/*!*******************************************************!*\
  !*** ./src/app/[locale]/client/domains/[id]/page.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DomainDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var _components_domains_DomainRegistrationStatus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/domains/DomainRegistrationStatus */ \"(app-pages-browser)/./src/components/domains/DomainRegistrationStatus.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction DomainDetailPage(param) {\n    let { params } = param;\n    _s();\n    const { id } = params;\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)(\"client\");\n    const dt = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)(\"client.domainWrapper\");\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getDomainDetails = async ()=>{\n            try {\n                setLoading(true);\n                // Get domain details from the API\n                const res = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getUserDomains();\n                if (res.data && res.data.domains) {\n                    // Find the domain with the matching ID\n                    const domainData = res.data.domains.find((domain)=>domain.id === id);\n                    if (domainData) {\n                        // Add default values for any missing properties\n                        const domain = {\n                            ...domainData,\n                            contacts: domainData.contacts || {\n                                registrant: {\n                                    name: \"Domain Owner\",\n                                    email: \"<EMAIL>\",\n                                    phone: \"+1234567890\",\n                                    address: \"123 Main St, City, Country\"\n                                },\n                                admin: {\n                                    name: \"Domain Admin\",\n                                    email: \"<EMAIL>\",\n                                    phone: \"+1234567890\",\n                                    address: \"123 Main St, City, Country\"\n                                },\n                                technical: {\n                                    name: \"Technical Contact\",\n                                    email: \"<EMAIL>\",\n                                    phone: \"+1987654321\",\n                                    address: \"456 Tech St, City, Country\"\n                                }\n                            },\n                            dnsRecords: domainData.dnsRecords || [\n                                {\n                                    id: \"rec1\",\n                                    type: \"A\",\n                                    name: \"@\",\n                                    content: \"***********\",\n                                    ttl: 3600\n                                },\n                                {\n                                    id: \"rec2\",\n                                    type: \"CNAME\",\n                                    name: \"www\",\n                                    content: \"@\",\n                                    ttl: 3600\n                                }\n                            ],\n                            nameservers: domainData.nameservers || [\n                                \"ns1.ztech.com\",\n                                \"ns2.ztech.com\"\n                            ]\n                        };\n                        setDomain(domain);\n                    }\n                }\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error getting domain details\", error);\n                setLoading(false);\n            }\n        };\n        getDomainDetails();\n    }, [\n        id\n    ]);\n    const handleAutoRenewToggle = async (value)=>{\n        try {\n            // This would be replaced with actual API call when implemented\n            // await domainMngService.toggleAutoRenewal(id, value);\n            setDomain({\n                ...domain,\n                autoRenew: value\n            });\n        } catch (error) {\n            console.error(\"Error toggling auto renewal\", error);\n        }\n    };\n    const handlePrivacyToggle = async (value)=>{\n        try {\n            // This would be replaced with actual API call when implemented\n            // await domainMngService.togglePrivacyProtection(id, value);\n            setDomain({\n                ...domain,\n                privacyProtection: value\n            });\n        } catch (error) {\n            console.error(\"Error toggling privacy protection\", error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-6 w-6 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        variant: \"h6\",\n                        className: \"text-gray-600\",\n                        children: [\n                            t(\"loading\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this);\n    }\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-screen p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                    variant: \"h4\",\n                    className: \"text-gray-800 font-bold mb-2\",\n                    children: t(\"domain_not_found\", {\n                        defaultValue: \"Domain Not Found\"\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    className: \"mt-4 bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 bg-gray-50 min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"text\",\n                    className: \"mb-6 text-blue-600 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-6 w-6 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                            variant: \"h1\",\n                                            className: \"text-2xl font-bold text-gray-800\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize mr-2 \".concat(domain.status === \"active\" ? \"bg-green-100 text-green-800\" : domain.status === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : domain.status === \"expired\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: dt(domain.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        dt(\"registrar\"),\n                                                        \": \",\n                                                        domain.registrar\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outlined\",\n                                    className: \"border-blue-600 text-blue-600 hover:bg-blue-50 flex items-center gap-2\",\n                                    onClick: ()=>window.open(\"http://\".concat(domain.name), \"_blank\"),\n                                    children: [\n                                        t(\"visit_website\", {\n                                            defaultValue: \"Visit Website\"\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                    onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/renew\")),\n                                    children: [\n                                        dt(\"renew_domain\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                    value: activeTab,\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsHeader, {\n                            className: \"bg-gray-100 rounded-lg p-1\",\n                            indicatorProps: {\n                                className: \"bg-white shadow-md rounded-md\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"overview\",\n                                    onClick: ()=>setActiveTab(\"overview\"),\n                                    className: activeTab === \"overview\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"overview\", {\n                                        defaultValue: \"Overview\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"dns\",\n                                    onClick: ()=>setActiveTab(\"dns\"),\n                                    className: activeTab === \"dns\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"dns_settings\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"contacts\",\n                                    onClick: ()=>setActiveTab(\"contacts\"),\n                                    className: activeTab === \"contacts\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"domain_contacts\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsBody, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"overview\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"domain_details\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"domain_name\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 267,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 270,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"status\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 275,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize \".concat(domain.status === \"active\" ? \"bg-green-100 text-green-800\" : domain.status === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : domain.status === \"expired\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                            children: dt(domain.status)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 278,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"registration_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 293,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: new Date(domain.registrationDate).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 296,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"expiry_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: new Date(domain.expiryDate).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"auto_renew\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 311,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                            checked: domain.autoRenew,\n                                                                            onChange: (e)=>handleAutoRenewToggle(e.target.checked),\n                                                                            color: \"blue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 314,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"whois_privacy\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 323,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                            checked: domain.privacyProtection,\n                                                                            onChange: (e)=>handlePrivacyToggle(e.target.checked),\n                                                                            color: \"blue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"nameservers\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                domain.nameservers.map((ns, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"NS \",\n                                                                                    index + 1\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 349,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"font-medium\",\n                                                                                children: ns\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 25\n                                                                    }, this)),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"outlined\",\n                                                                        className: \"w-full border-blue-600 text-blue-600 hover:bg-blue-50\",\n                                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/nameservers\")),\n                                                                        children: dt(\"update_nameservers\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"dns\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: dt(\"manage_dns_records\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            className: \"bg-blue-600 hover:bg-blue-700\",\n                                                            onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns/add\")),\n                                                            children: t(\"add_record\", {\n                                                                defaultValue: \"Add Record\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"overflow-x-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    className: \"bg-gray-50 border-b border-gray-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                            children: t(\"type\", {\n                                                                                defaultValue: \"Type\"\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                            children: t(\"name\", {\n                                                                                defaultValue: \"Name\"\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 396,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                            children: t(\"content\", {\n                                                                                defaultValue: \"Content\"\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 399,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                            children: t(\"ttl\", {\n                                                                                defaultValue: \"TTL\"\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 402,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-3 text-right text-sm font-medium text-gray-500\",\n                                                                            children: t(\"actions\", {\n                                                                                defaultValue: \"Actions\"\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 405,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                className: \"divide-y divide-gray-200\",\n                                                                children: domain.dnsRecords.map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"hover:bg-gray-50\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-sm font-medium text-gray-900\",\n                                                                                children: record.type\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 413,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                children: record.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 416,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                children: record.content\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 419,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                children: record.ttl\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 422,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-right\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: \"text\",\n                                                                                    className: \"text-blue-600\",\n                                                                                    onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns/\").concat(record.id)),\n                                                                                    children: t(\"edit\", {\n                                                                                        defaultValue: \"Edit\"\n                                                                                    })\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 426,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 425,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, record.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 412,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"contacts\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: dt(\"domain_contacts\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"registrant\", {\n                                                                        defaultValue: \"Registrant Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 457,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.contacts.registrant.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 463,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.registrant.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 466,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.registrant.phone\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 469,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.registrant.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 472,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"admin\", {\n                                                                        defaultValue: \"Administrative Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.contacts.admin.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 482,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.admin.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.admin.phone\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 488,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.admin.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"technical\", {\n                                                                        defaultValue: \"Technical Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.contacts.technical.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 501,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.technical.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 504,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.technical.phone\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 507,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: domain.contacts.technical.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 510,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/contacts\")),\n                                                        children: dt(\"update_contacts\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainDetailPage, \"0gqgV+UVPjyUaY/PznjCm7ieLM8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DomainDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DomainDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/domains/DomainRegistrationStatus.jsx":
/*!*************************************************************!*\
  !*** ./src/components/domains/DomainRegistrationStatus.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\n * Component to display domain registration status and allow manual registration\n */ const DomainRegistrationStatus = (param)=>{\n    let { domain, onRegistrationComplete } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { t } = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)();\n    const handleRegisterDomain = async ()=>{\n        if (!domain.orderId) {\n            setError(\"Order ID is missing\");\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].registerDomainsForOrder(domain.orderId);\n            if (response.data.success) {\n                setSuccess(\"Domain registration initiated successfully\");\n                if (onRegistrationComplete) {\n                    onRegistrationComplete(response.data);\n                }\n            } else {\n                setError(response.data.message || \"Failed to register domain\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error registering domain:\", error);\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"An error occurred while registering the domain\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Determine if registration is needed based on domain status\n    const registrationNeeded = domain.status === \"pending\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-4\",\n        children: [\n            domain.status === \"active\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"green\",\n                className: \"mb-4\",\n                children: t(\"domain_active_message\", \"Your domain is active and ready to use.\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, undefined) : domain.status === \"pending\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"yellow\",\n                className: \"mb-4\",\n                children: t(\"domain_pending_message\", \"Your domain registration is pending. Click the button below to complete registration.\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"gray\",\n                className: \"mb-4\",\n                children: t(\"domain_status_message\", \"Domain status: \".concat(domain.status))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"red\",\n                className: \"mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, undefined),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"green\",\n                className: \"mb-4\",\n                children: success\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, undefined),\n            registrationNeeded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                className: \"bg-blue-600 hover:bg-blue-700\",\n                onClick: handleRegisterDomain,\n                disabled: loading,\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-4 w-4 mr-2 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                            lineNumber: 95,\n                            columnNumber: 15\n                        }, undefined),\n                        t(\"registering_domain\", \"Registering Domain...\")\n                    ]\n                }, void 0, true) : t(\"complete_domain_registration\", \"Complete Domain Registration\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainRegistrationStatus, \"VNLlb64ArGb6ZUjZgo+DcrVkX2k=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations\n    ];\n});\n_c = DomainRegistrationStatus;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainRegistrationStatus);\nvar _c;\n$RefreshReg$(_c, \"DomainRegistrationStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/DomainRegistrationStatus.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/loader-circle.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ LoaderCircle; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21 12a9 9 0 1 1-6.219-8.56\",\n            key: \"13zald\"\n        }\n    ]\n];\nconst LoaderCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"LoaderCircle\", __iconNode);\n //# sourceMappingURL=loader-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbG9hZGVyLWNpcmNsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVxRDtBQUV0RCxNQUFNQyxhQUFhO0lBQUM7UUFBQztRQUFRO1lBQUVDLEdBQUc7WUFBK0JDLEtBQUs7UUFBUztLQUFFO0NBQUM7QUFDbEYsTUFBTUMsZUFBZUosZ0VBQWdCQSxDQUFDLGdCQUFnQkM7QUFFUCxDQUMvQyx5Q0FBeUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9sb2FkZXItY2lyY2xlLmpzPzQwM2QiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuNDc1LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBfX2ljb25Ob2RlID0gW1tcInBhdGhcIiwgeyBkOiBcIk0yMSAxMmE5IDkgMCAxIDEtNi4yMTktOC41NlwiLCBrZXk6IFwiMTN6YWxkXCIgfV1dO1xuY29uc3QgTG9hZGVyQ2lyY2xlID0gY3JlYXRlTHVjaWRlSWNvbihcIkxvYWRlckNpcmNsZVwiLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IHsgX19pY29uTm9kZSwgTG9hZGVyQ2lyY2xlIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWxvYWRlci1jaXJjbGUuanMubWFwXG4iXSwibmFtZXMiOlsiY3JlYXRlTHVjaWRlSWNvbiIsIl9faWNvbk5vZGUiLCJkIiwia2V5IiwiTG9hZGVyQ2lyY2xlIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\n"));

/***/ })

});