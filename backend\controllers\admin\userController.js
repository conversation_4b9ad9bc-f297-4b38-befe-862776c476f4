const mongoose = require("mongoose");
const User = require("../../models/User");
const Cart = require("../../models/Cart");
const Order = require("../../models/Order");
const Ticket = require("../../models/Ticket");
const { v4: uuidv4 } = require("uuid");
const crypto = require("crypto");
const { sendVerificationEmail } = require("../../routes/sendEmail/sendEmail");
const SubOrder = require("../../models/SubOrder");
const adminLogger = require("../../utils/adminLogger");

// Get All Users with filters
exports.getAllUsers = async (req, res) => {
  try {
    console.log(req.query.params);
    const { page = 1, limit = 10, role, state, search } = req.query.params;

    const skip = (page - 1) * limit;
    // Build query object
    let query = {};
    // Add role filter if provided
    if (role && role !== "all") {
      query.role = role;
    }
    // Add state filter if provided
    if (state && state !== "all") {
      query.state = state;
    }
    // Add search filter for name or email
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: "i" } },
        { lastName: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } },
      ];
    }

    const [users, total] = await Promise.all([
      User.find(query)
        .select("-hashed_password")
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
      User.countDocuments(query),
    ]);

    return res.status(200).json({
      data: users,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit),
      },
    });
  } catch (error) {
    console.error("Error fetching users:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

// Add a New User with validation
exports.addUser = async (req, res) => {
  try {
    const { email, password, firstName, lastName, role, state } = req.body;

    // Check if email already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ error: "Email already exists" });
    }

    // Password validation
    if (!password || password.length < 8) {
      return res.status(400).json({
        error: "Password must be at least 8 characters long",
      });
    }

    // Construct user data
    const userData = {
      firstName,
      lastName,
      email,
      password,
      billingInfo: {
        BillToName: `${firstName} ${lastName}`,
        email,
        phone: "",
        address: "",
        country: "",
      },
      role: role || "Customer", // Default role
      state: state || "NOT_VERIFIED", // Default state
    };

    // Create new user
    const user = new User(userData);
    const savedUser = await user.save();

    // Send verification email
    const uniqueString = uuidv4().replace(/-/g, Math.random());
    const hashedUniqueString = cryptUniqueString(uniqueString, savedUser._id);

    await sendVerificationEmail(
      req,
      savedUser.email,
      uniqueString,
      savedUser._id
    );
    await User.findOneAndUpdate(
      { _id: savedUser._id },
      { hashedUniqueStringVerifyEmail: hashedUniqueString },
      { new: true }
    );

    // Remove sensitive data
    savedUser.salt = undefined;
    savedUser.hashed_password = undefined;
    savedUser.hashedUniqueStringVerifyEmail = undefined;

    // Log admin activity using the enhanced logger
    await adminLogger.logCreateUser(
      req.user?._id,
      {
        firstName: savedUser.firstName,
        lastName: savedUser.lastName,
      },
      savedUser.toObject()
    );

    return res.status(201).json(savedUser);
  } catch (error) {
    console.error("Error adding user:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

// Modify a User with validation
exports.modifyUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const updateData = req.body;

    // Validate user ID
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({ error: "Invalid user ID" });
    }

    // Check if user exists
    const existingUser = await User.findById(userId);
    if (!existingUser) {
      return res.status(404).json({ error: "User not found" });
    }

    // Check if email is being updated and already exists
    if (updateData.email && updateData.email !== existingUser.email) {
      const emailExists = await User.findOne({
        email: updateData.email,
        _id: { $ne: userId },
      });
      if (emailExists) {
        return res.status(400).json({ error: "Email already exists" });
      }
    }

    // Password validation if being updated
    if (updateData.password) {
      if (updateData.password.length < 8) {
        return res.status(400).json({
          error: "Password must be at least 8 characters long",
        });
      }
      const user = await User.findById(userId).select("+salt");
      user.password = updateData.password;
      delete updateData.password;
      updateData.hashed_password = user.hashed_password;
    }

    // Update the user document
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { $set: updateData },
      { new: true, runValidators: true }
    ).select("-hashed_password -salt");

    // Log admin activity using the enhanced logger
    await adminLogger.logUpdateUser(
      req.user?._id,
      {
        firstName: existingUser.firstName,
        lastName: existingUser.lastName,
      },
      updatedUser.toObject()
    );

    return res.status(200).json(updatedUser);
  } catch (error) {
    console.error("Error modifying user:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

// Delete User (unchanged as it was already well-implemented)
exports.deleteUser = async (req, res) => {
  try {
    const { userId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({ error: "Invalid user ID" });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    const userOrders = await Order.find({ user: userId }).select("subOrders");
    const subOrderIds = userOrders.reduce((acc, order) => {
      return acc.concat(order.subOrders.map((id) => id.toString()));
    }, []);

    await Promise.all([
      subOrderIds.length > 0
        ? SubOrder.deleteMany({ _id: { $in: subOrderIds } })
        : Promise.resolve(),
      Order.deleteMany({ user: userId }),
      Cart.deleteMany({ user: userId }),
      Ticket.deleteMany({ creator: userId }),
    ]);

    await User.findByIdAndDelete(userId);

    // Log admin activity using the enhanced logger
    await adminLogger.logDeleteUser(
      req.user?._id,
      {
        firstName: user.firstName,
        lastName: user.lastName,
      },
      user.toObject()
    );

    return res.status(200).json({
      message: "User and related data deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting user:", error);
    return res.status(500).json({
      error: "Internal server error",
      details: error.message,
    });
  }
};

const cryptUniqueString = (uniqueString, userId) => {
  return crypto.createHash("sha256", userId).update(uniqueString).digest("hex");
};
