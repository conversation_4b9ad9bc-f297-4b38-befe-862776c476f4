# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
node_modules/bin
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build
.env.production
# misc
.DS_Store
*.pem
.env.production
# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# /public/images/uploads/*
# !public/images/uploads/.gitkeep
