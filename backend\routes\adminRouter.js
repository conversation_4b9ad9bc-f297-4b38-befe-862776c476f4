const express = require("express");
const {
  getAllUsers,
  addUser,
  deleteUser,
  modifyUser,
} = require("../controllers/admin/userController");
const {
  getAllPackages,
  addPackage,
  updatePackage,
  deletePackage,
  addSpec,
  getAllSpecs,
  updateSpec,
  deeplTranslate,
  getAllCategories,
} = require("../controllers/admin/packController");
const adminRouter = express.Router();
const { asyncBackFrontEndLang } = require("../midelwares/sharedMidd");
const { adminMiddleware } = require("../midelwares/authorization");
const { registerValidator } = require("../midelwares/requests/authRequest");
const {
  addUserValidator,
  updateUserValidator,
} = require("../midelwares/admin/userValidator");
const {
  getAllOrders,
  updateOrderStatus,
  getOrderById,
  updateSubOrderStatus,
} = require("../controllers/admin/orderController");
const {
  getDashboardStats,
  getPackageDistributionByCategory
} = require("../controllers/admin/dashboardController");
const {
  getAdminActivityLogs,
  getActivityLogFilters,
} = require("../controllers/admin/logController");
const {
  getAllConversations,
  getConversationById,
  deleteConversation,
  deleteMultipleConversations,
  deleteAllConversations,
  getUserConversations
} = require("../controllers/admin/chatController");

const {
  getContext,
  updateContext
} = require("../controllers/admin/chatbotContextController");

// Get All Users
adminRouter.get(
  "/users",
  asyncBackFrontEndLang,
  adminMiddleware,
  getAllUsers
);

// Add a New User
adminRouter.post(
  "/users",
  asyncBackFrontEndLang,
  adminMiddleware,
  addUserValidator,
  addUser
);

// Delete a User
adminRouter.delete(
  "/users/:userId",
  asyncBackFrontEndLang,
  adminMiddleware,
  deleteUser
);

// Modify a User
adminRouter.put(
  "/users/:userId",
  asyncBackFrontEndLang,
  adminMiddleware,
  updateUserValidator,
  modifyUser
);

// Get All Packages
adminRouter.get(
  "/packages",
  asyncBackFrontEndLang,
  adminMiddleware,
  getAllPackages
);

// Add a New Package
adminRouter.post(
  "/packages",
  asyncBackFrontEndLang,
  adminMiddleware,
  addPackage
);

// Update a Package
adminRouter.put(
  "/packages/:packageId",
  asyncBackFrontEndLang,
  adminMiddleware,
  updatePackage
);

// Delete a Package
adminRouter.delete(
  "/packages/:packageId",
  asyncBackFrontEndLang,
  adminMiddleware,
  deletePackage
);

// Add a New Spec
adminRouter.post("/specs", asyncBackFrontEndLang, adminMiddleware, addSpec);

// Get All Specs
adminRouter.get("/specs", asyncBackFrontEndLang, adminMiddleware, getAllSpecs);

adminRouter.put("/update-spec", adminMiddleware, updateSpec);

adminRouter.put("/update-spec", adminMiddleware, updateSpec);

adminRouter.get(
  "/categories",
  asyncBackFrontEndLang,
  adminMiddleware,
  getAllCategories
);

// GET ALL ORDERS WITH PAGINATION
adminRouter.get(
  "/orders",
  asyncBackFrontEndLang,
  adminMiddleware,
  getAllOrders
);

// UPDATE ORDER STATUS
adminRouter.put(
  "/orders/:orderId/status",
  asyncBackFrontEndLang,
  adminMiddleware,
  updateOrderStatus
);

// UPDATE SUBORDER STATUS
adminRouter.put(
  "/orders/:orderId/:suborderId/status",
  asyncBackFrontEndLang,
  adminMiddleware,
  updateSubOrderStatus
);

// GET A SPECIFIC ORDER
adminRouter.get(
  "/orders/:orderId",
  asyncBackFrontEndLang,
  adminMiddleware,
  getOrderById
);

// Dashboard Stats
adminRouter.get(
  "/dashboard/stats",
  asyncBackFrontEndLang,
  adminMiddleware,
  getDashboardStats
);

// Activity logs routes
adminRouter.get(
  "/activity-logs",
  asyncBackFrontEndLang,
  adminMiddleware,
  getAdminActivityLogs
);
adminRouter.get(
  "/activity-logs/filters",
  asyncBackFrontEndLang,
  adminMiddleware,
  getActivityLogFilters
);

// GET ALL CHAT CONVERSATIONS
adminRouter.get(
  "/chats",
  asyncBackFrontEndLang,
  adminMiddleware,
  getAllConversations
);

// GET A SPECIFIC CHAT CONVERSATION
adminRouter.get(
  "/chats/:conversationId",
  asyncBackFrontEndLang,
  adminMiddleware,
  getConversationById
);

// DELETE A SPECIFIC CHAT CONVERSATION
adminRouter.delete(
  "/chats/:conversationId",
  asyncBackFrontEndLang,
  adminMiddleware,
  deleteConversation
);

// DELETE MULTIPLE CHAT CONVERSATIONS
adminRouter.post(
  "/chats/delete-multiple",
  asyncBackFrontEndLang,
  adminMiddleware,
  deleteMultipleConversations
);

// DELETE ALL CHAT CONVERSATIONS
adminRouter.delete(
  "/chats",
  asyncBackFrontEndLang,
  adminMiddleware,
  deleteAllConversations
);

// GET USER CHAT CONVERSATIONS
adminRouter.get(
  "/users/:userId/chats",
  asyncBackFrontEndLang,
  adminMiddleware,
  getUserConversations
);

// GET CHATBOT CONTEXT
adminRouter.get(
  "/chatbot-context",
  asyncBackFrontEndLang,
  adminMiddleware,
  getContext
);

// UPDATE CHATBOT CONTEXT
adminRouter.put(
  "/chatbot-context",
  asyncBackFrontEndLang,
  adminMiddleware,
  updateContext
);

adminRouter.post("/deepl-translate", adminMiddleware, deeplTranslate);

adminRouter.get(
  "/dashboard/package-distribution",
  asyncBackFrontEndLang,
  adminMiddleware,
  getPackageDistributionByCategory
);
module.exports = adminRouter;
