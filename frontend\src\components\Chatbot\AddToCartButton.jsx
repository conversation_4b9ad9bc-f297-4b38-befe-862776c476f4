import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { ShoppingCart, LogIn, Calendar } from 'lucide-react';
import { useAuth } from '@/app/context/AuthContext';
import cartService from '@/app/services/cartService';
import { toast } from 'react-toastify';
import { useRouter } from 'next/navigation';

const AddToCartButton = ({ packageId, packageName, period = 12 }) => {
  const { setCartCount, isAuthenticated } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const handleAddToCart = async () => {
    // If user is not authenticated, redirect to login page
    if (!isAuthenticated) {
      const confirmLogin = window.confirm('You need to be logged in to add items to your cart. Would you like to log in now?');
      if (confirmLogin) {
        // Store package info in localStorage to potentially add it after login
        localStorage.setItem('pendingCartItem', JSON.stringify({
          packageId,
          packageName,
          period
        }));
        router.push('/login?redirect=client/cart');
      }
      return;
    }

    setIsLoading(true);
    try {
      const response = await cartService.addItemToCart({
        packageId,
        quantity: 1,
        period: period, // Use the provided period (default is 12 months/1 year)
      });

      setCartCount(response.data.cart.cartCount);

      // Show success message without confirmation dialog
      toast.success(`${packageName} added to cart!`, {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });

      // Set success state temporarily
      setIsSuccess(true);
      setTimeout(() => {
        setIsSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error adding to cart:', error);

      // Handle authentication errors
      if (error.response?.status === 401) {
        const confirmLogin = window.confirm('Your session has expired. Please log in again to add items to your cart. Would you like to log in now?');
        if (confirmLogin) {
          // Store package info in localStorage to potentially add it after login
          localStorage.setItem('pendingCartItem', JSON.stringify({
            packageId,
            packageName,
            period
          }));
          router.push('/login?redirect=client/cart');
        }
      } else {
        toast.error(error.response?.data?.message || 'Error adding to cart');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mt-3 mb-2">
      <div className="p-3 border border-blue-100 bg-blue-50 rounded-lg">
        <div className="flex items-center mb-2">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
            <ShoppingCart size={16} className="text-blue-600" />
          </div>
          <div className="flex flex-col">
            <span className="text-sm font-medium text-gray-700">
              {packageName}
            </span>
            <div className="flex items-center text-xs text-gray-500">
              <Calendar size={12} className="mr-1" />
              {period === 1 ? '1 month' : period === 12 ? '1 year' : `${period} months`}
            </div>
          </div>
        </div>
        <button
          onClick={handleAddToCart}
          disabled={isLoading || isSuccess}
          className={`w-full flex items-center justify-center gap-2 px-4 py-2 rounded-md transition-all duration-300 ${
            isLoading
              ? "bg-gray-300 text-gray-500 cursor-not-allowed"
              : isSuccess
                ? "bg-green-500 text-white"
                : isAuthenticated
                  ? "bg-[#497ef7] hover:bg-[#3968d8] text-white"
                  : "bg-gray-700 hover:bg-gray-800 text-white"
          }`}
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Adding to cart...
            </>
          ) : isSuccess ? (
            <>
              <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              Added to cart!
            </>
          ) : isAuthenticated ? (
            <>
              <ShoppingCart size={16} />
              Add to Cart {period === 1 ? '(Monthly)' : period === 12 ? '(Yearly)' : `(${period} months)`}
            </>
          ) : (
            <>
              <LogIn size={16} />
              Login to Add to Cart
            </>
          )}
        </button>
      </div>
    </div>
  );
};

AddToCartButton.propTypes = {
  packageId: PropTypes.string.isRequired,
  packageName: PropTypes.string.isRequired,
  period: PropTypes.number
};

export default AddToCartButton;
