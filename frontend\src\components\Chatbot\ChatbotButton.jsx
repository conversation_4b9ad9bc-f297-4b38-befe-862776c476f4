import React from 'react';
import PropTypes from 'prop-types';
import './Chatbot.css';
// testing 
const ChatbotButton = ({ onClick, isMinimized = false, isOpen = false }) => {
  return (
    <button 
      onClick={onClick}
      className={`bg-[#497ef7] hover:bg-[#3968d8] text-white p-3 rounded-full transition-all duration-300 shadow-lg flex items-center justify-center ${isOpen ? 'bg-[#3968d8]' : ''}`}
      style={{ 
        width: '46px', 
        height: '46px',
        boxShadow: '0 4px 12px rgba(73, 126, 247, 0.3)'
      }}
      aria-label="Chat with AI Assistant"
      title={isOpen ? "Close chat" : "Chat with ZTech Assistant"}
    >
      {isMinimized && (
        <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></span>
      )}
      
      {!isMinimized && !isOpen && (
        <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></span>
      )}
      
      {isOpen ? (
        <svg 
          viewBox="0 0 24 24" 
          width="20" 
          height="20" 
          stroke="currentColor" 
          strokeWidth="2" 
          fill="none"
        >
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      ) : (
        <svg 
          viewBox="0 0 24 24" 
          width="20" 
          height="20" 
          stroke="currentColor" 
          strokeWidth="2" 
          fill="none"
          className="translate-x-[1px]"
        >
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
          <circle cx="12" cy="10" r="1"></circle>
          <circle cx="8" cy="10" r="1"></circle>
          <circle cx="16" cy="10" r="1"></circle>
        </svg>
      )}
    </button>
  );
};

ChatbotButton.propTypes = {
  onClick: PropTypes.func.isRequired,
  isMinimized: PropTypes.bool,
  isOpen: PropTypes.bool
};

export default ChatbotButton;