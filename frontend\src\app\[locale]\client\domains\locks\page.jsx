"use client";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import {
  Typo<PERSON>,
  <PERSON>,
  CardBody,
  Button,
  Switch,
} from "@material-tailwind/react";
import { ArrowLeft, Lock } from "lucide-react";
import domainMngService from "@/app/services/domainMngService";

export default function DomainLocksPage() {
  const t = useTranslations("client");
  const dt = useTranslations("client.domainWrapper");
  const [domains, setDomains] = useState([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const getUserDomains = async () => {
      try {
        // This would be replaced with actual API call when implemented
        // const res = await domainMngService.getUserDomains();
        // setDomains(res.data.domains);

        // For now, using mock data
        setTimeout(() => {
          setDomains([
            {
              id: "dom1",
              name: "example.com",
              status: "active",
              locked: true,
            },
            {
              id: "dom2",
              name: "mywebsite.net",
              status: "active",
              locked: false,
            },
            {
              id: "dom3",
              name: "businesssite.org",
              status: "pending",
              locked: true,
            },
          ]);
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error("Error getting domains", error);
        setLoading(false);
      }
    };
    getUserDomains();
  }, []);

  const handleLockToggle = async (domainId, locked) => {
    try {
      // This would be replaced with actual API call when implemented
      // await domainMngService.toggleDomainLock(domainId, locked);

      // For now, just update the state
      setDomains(
        domains.map((domain) =>
          domain.id === domainId ? { ...domain, locked } : domain
        )
      );
    } catch (error) {
      console.error("Error toggling domain lock", error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Lock className="h-6 w-6 text-blue-600" />
          </div>
          <Typography variant="h6" className="text-gray-600">
            {t("loading")}...
          </Typography>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        <Button
          variant="text"
          className="mb-6 text-blue-600 flex items-center gap-2"
          onClick={() => router.push("/client/domains")}
        >
          <ArrowLeft className="h-4 w-4" />
          {dt("back_to_domains")}
        </Button>

        <div className="mb-8">
          <Typography variant="h1" className="text-3xl font-bold text-gray-800">
            {dt("domain_locks")}
          </Typography>
          <Typography className="text-gray-600 mt-1">
            {t("domain_locks_description", {
              defaultValue:
                "Protect your domains from unauthorized transfers and changes.",
            })}
          </Typography>
        </div>

        <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
          <CardBody className="p-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-50 border-b border-gray-200">
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-500">
                      {dt("domain_name")}
                    </th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-500">
                      {dt("status")}
                    </th>
                    <th className="px-6 py-4 text-right text-sm font-medium text-gray-500">
                      {t("lock_status", { defaultValue: "Lock Status" })}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {domains.map((domain) => (
                    <tr key={domain.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Lock className="h-5 w-5 text-blue-600" />
                          </div>
                          <Typography className="ml-3 font-medium text-gray-900">
                            {domain.name}
                          </Typography>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${
                            domain.status === "active"
                              ? "bg-green-100 text-green-800"
                              : domain.status === "pending"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {dt(domain.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-right">
                        <div className="flex items-center justify-end">
                          <Typography className="mr-3 text-sm text-gray-500">
                            {domain.locked ? dt("locked") : dt("unlocked")}
                          </Typography>
                          <Switch
                            checked={domain.locked}
                            onChange={(e) =>
                              handleLockToggle(domain.id, e.target.checked)
                            }
                            color="blue"
                            disabled={domain.status !== "active"}
                          />
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardBody>
        </Card>

        <div className="mt-8">
          <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
            <CardBody className="p-6">
              <Typography className="text-lg font-medium text-gray-900 mb-4">
                {t("about_domain_locks", {
                  defaultValue: "About Domain Locks",
                })}
              </Typography>
              <Typography className="text-gray-600 mb-4">
                {t("domain_locks_info", {
                  defaultValue:
                    "Domain locks protect your domains from unauthorized transfers to another registrar. When a domain is locked, it cannot be transferred until it is unlocked.",
                })}
              </Typography>
              <Typography className="text-gray-600">
                {t("domain_locks_recommendation", {
                  defaultValue:
                    "We recommend keeping your domains locked unless you are actively transferring them to another registrar.",
                })}
              </Typography>
            </CardBody>
          </Card>
        </div>
      </div>
    </div>
  );
}
