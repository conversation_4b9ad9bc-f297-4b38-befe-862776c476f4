const { GoogleGenerativeAI } = require('@google/generative-ai');
const chatbotCacheService = require('../services/chatbotCacheService');
const ChatConversation = require('../models/ChatConversation');
const ChatbotContext = require('../models/ChatbotContext');
const jwt = require('jsonwebtoken');

// API configuration
const API_KEY = process.env.GOOGLE_AI_API_KEY; // Reusing the same env variable for now
// You may want to create a new env variable like GOOGLE_AI_API_KEY later
const MODEL = 'gemini-2.0-flash'; // Using the Gemini 2.0 Flash model

// Initialize the Google GenAI client
const genAI = new GoogleGenerativeAI(API_KEY);
const model = genAI.getGenerativeModel({ model: MODEL });

/**
 * Get formatted package information for the chatbot context
 * @returns {Promise<string>} Formatted package information
 */
async function getPackageContext() {
  try {
    const packages = await chatbotCacheService.getPackages();

    if (!packages || packages.length === 0) {
      return "Currently, there are no packages available in our system. Please contact our sales team for the most up-to-date information on our products and services.";
    }

    // Format packages into a readable context string
    let packageContext = "CURRENT PACKAGE PRICING:\n\n";

    // Group packages by category
    const packagesByCategory = {};
    packages.forEach(pkg => {
      const category = pkg.category || 'Uncategorized';
      if (!packagesByCategory[category]) {
        packagesByCategory[category] = [];
      }
      packagesByCategory[category].push(pkg);
    });

    // Format each category and its packages
    Object.entries(packagesByCategory).forEach(([category, pkgs]) => {
      packageContext += `${category} Packages:\n`;

      pkgs.forEach(pkg => {
        // Include package ID in a special format that can be parsed by the frontend
        packageContext += `- ${pkg.name}: MAD ${pkg.price} (Regular: MAD ${pkg.regularPrice}) [[PACKAGE_ID:${pkg.id}:PACKAGE_NAME:${pkg.name}]]\n`;
        packageContext += `  Description: ${pkg.description}\n`;

        // Add SSL type information if available
        if (pkg.sslType) {
          packageContext += `  SSL Type: ${pkg.sslType}\n`;
        }

        // Add discount information if available
        if (pkg.discounts && pkg.discounts.length > 0) {
          packageContext += `  Discounts:\n`;
          pkg.discounts.forEach(discount => {
            packageContext += `    - ${discount.percentage}% off for ${discount.period} month${discount.period > 1 ? 's' : ''}\n`;
          });
        }

        // Add specifications if available
        if (pkg.specifications && pkg.specifications.length > 0) {
          packageContext += `  Specifications:\n`;
          pkg.specifications.forEach(spec => {
            if (spec.name && spec.value) {
              packageContext += `    - ${spec.name}: ${spec.value}\n`;
            }
          });
        }

        packageContext += '\n';
      });
    });

    return packageContext;
  } catch (error) {
    console.error('Error generating package context:', error);
    return 'Package information is temporarily unavailable. Please contact our sales team for current pricing.';
  }
}

// Create a function to generate the system prompt with package data
async function generateSystemPrompt() {
  // Get package context
  const packageContext = await getPackageContext();

  // Get company context from database
  let contextContent;
  try {
    const contextDoc = await ChatbotContext.getDefaultContext();
    contextContent = contextDoc.content;
  } catch (error) {
    console.error('Error getting chatbot context from database:', error);
    contextContent = 'ZTech Engineering is a Moroccan technology company specializing in web development, cloud services, hosting, and cybersecurity.';
  }

  // Build the system prompt with package data included
  return `You are an AI assistant for ZTech Engineering, a Moroccan technology company specializing in web development,
cloud services, hosting, and cybersecurity. Your name is "ZTech Assistant".

Be helpful, friendly, and professional. Provide concise answers focused on ZTech's services.

IMPORTANT FORMATTING INSTRUCTIONS:
1. Use proper HTML tags for all formatting:
   - <b>bold text</b> for emphasis and service names
   - <br> for line breaks between paragraphs
   - <ul> and <li> for bullet points
   - <p> for paragraphs
2. DO NOT use markdown formatting with asterisks like **text** or links like [text](url).
3. Use proper spacing and layout with HTML tags to make your responses visually organized.

LINK FORMATTING INSTRUCTIONS:
1. For internal links to pages within the ZTech website, use the special format: [[ROUTE:/path/to/page:TITLE:Link Text]]
   - Example: [[ROUTE:/hosting/plans:TITLE:View our hosting plans]]
   - Example: [[ROUTE:/client/cart:TITLE:View your cart]]
2. For external links to other websites, use regular HTML anchor tags:
   - Example: <a href="https://example.com" target="_blank">Visit Example</a>
3. Always use the ROUTE format for internal navigation to ensure proper routing.

PACKAGE INFORMATION INSTRUCTIONS:
1. When discussing specific packages, ALWAYS include the package ID marker exactly as provided in the package information.
2. The basic package ID marker format is [[PACKAGE_ID:id:PACKAGE_NAME:name]].
3. When a user asks about a specific package with a specific billing period, use the extended format: [[PACKAGE_ID:id:PACKAGE_NAME:name:PERIOD:period]].
   - For example, if a user asks about a monthly plan: [[PACKAGE_ID:123:PACKAGE_NAME:Pro Hosting:PERIOD:1]]
   - For a yearly plan: [[PACKAGE_ID:123:PACKAGE_NAME:Pro Hosting:PERIOD:12]]
   - For other periods (e.g., 3 months): [[PACKAGE_ID:123:PACKAGE_NAME:Pro Hosting:PERIOD:3]]
4. If no specific period is mentioned by the user, default to yearly billing (12 months) by using the basic format without the PERIOD parameter.
5. When a user asks about a specific package or pricing, include the full package information with the ID marker.
6. Always place the ID marker immediately after mentioning the package name and price, with no space in between.
7. Do not modify or remove any part of the ID marker.

ADDITIONAL FORMATTING INSTRUCTIONS:
1. ALWAYS USE <ul> AND <li> TAGS WHEN LISTING SERVICES OR FEATURES:
   <p>ZTech Engineering offers these services:</p>
   <ul style="padding-left: 20px; margin-top: 5px; margin-bottom: 5px;">
     <li><b>Web Development:</b> Creating responsive websites and applications</li>
     <li><b>Cloud Services:</b> Reliable hosting solutions for businesses</li>
     <li><b>Cybersecurity:</b> Protection against online threats and vulnerabilities</li>
   </ul>

5. Always separate paragraphs with <br> tags for better readability.
6. LINKS: Format links with HTML anchor tags like this:
   <a href="https://ztechengineering.com/en/hosting" class="text-blue-600 hover:underline">Visit our Hosting page</a>
   DO NOT use markdown format like [Visit Hosting](https://ztechengineering.com/en/hosting)
7. When referring users to specific services, always use HTML links with helpful text:
   <p>For more details, <a href="https://ztechengineering.com/en/hosting" class="text-blue-600 hover:underline">visit our Hosting services page</a>.</p>

COMPANY CONTEXT:
${contextContent}

PACKAGE INFORMATION:
${packageContext}

ADDITIONAL GUIDELINES:
- Be precise about pricing information when asked.
- When asked about locations, mention both the Morocco and USA offices.
- Recommend contacting the sales team for custom quotes.
- For technical questions beyond your knowledge, offer to connect the user with the technical team.
- Always be respectful and avoid generating harmful or inappropriate content.
- When sharing links, make them visually appealing with appropriate styling.
- USE BULLET POINTS (<ul> and <li> tags) FREQUENTLY when providing lists of features, services, or options.`;
}

// Store chat sessions in memory (in production, use Redis or another persistent store)
const chatSessions = new Map();

const chatbotController = {
  processMessage: async (req, res) => {
    try {
      const { messages, sessionId } = req.body;

      // Generate a session ID if not provided
      const currentSessionId = sessionId || `session_${Date.now()}`;

      if (!messages || !Array.isArray(messages)) {
        return res.status(400).json({ error: 'Bad Request', message: 'Messages must be an array' });
      }

      // Get the last user message
      const lastMessage = messages[messages.length - 1];
      if (!lastMessage || lastMessage.role !== 'user') {
        return res.status(400).json({ error: 'Bad Request', message: 'Last message must be from user' });
      }

      const userInput = lastMessage.content;

      // Get or create a chat session
      let chatSession;
      if (chatSessions.has(currentSessionId)) {
        // Use existing session
        chatSession = chatSessions.get(currentSessionId);
      } else {
        // Create a new session with system prompt
        const systemPrompt = await generateSystemPrompt();

        // Initialize the chat with the system prompt
        chatSession = {
          chat: model.startChat({
            history: [
              {
                role: 'user',
                parts: [{ text: 'System information: ' + systemPrompt }],
              },
              {
                role: 'model',
                parts: [{ text: 'I understand. I will use this information to assist users.' }],
              }
            ],
            generationConfig: {
              temperature: 0.7,
              maxOutputTokens: 1500,
            },
          }),
          messages: [...messages.slice(0, -1)], // Store all messages except the last one (which we'll process now)
          systemPrompt
        };

        chatSessions.set(currentSessionId, chatSession);
      }

      // Add previous messages to history if they're not already there
      // This handles the case where the frontend sends the full history each time
      const existingMessageCount = chatSession.messages.length;
      const newMessages = messages.slice(0, -1).slice(existingMessageCount);

      if (newMessages.length > 0) {
        chatSession.messages.push(...newMessages);
      }

      // Add the current message to the session history
      chatSession.messages.push(lastMessage);

      // Send the message to the chat session
      const result = await chatSession.chat.sendMessage(userInput);
      const response = result.response;

      // Get the response text
      const responseText = response.text();

      // Create assistant message
      const assistantMessage = {
        role: 'assistant',
        content: responseText
      };

      // Add the assistant's response to the session history
      chatSession.messages.push(assistantMessage);

      // Format the response to match the expected format by the frontend
      const formattedResponse = {
        id: 'gemini-response-' + Date.now(),
        model: MODEL,
        sessionId: currentSessionId, // Return the session ID to the client
        choices: [
          {
            message: assistantMessage,
            index: 0,
          }
        ]
      };

      // Save the conversation to the database
      try {
        // Extract user ID from request if available
        let userIdToSave = null;

        // Check for authenticated user
        if (req.user && req.user.id) {
          userIdToSave = req.user.id;
        } else if (req.cookies && req.cookies.token) {
          // Try to extract user ID from token
          try {
            const decodedToken = jwt.verify(req.cookies.token, process.env.JWT_SECRET);
            userIdToSave = decodedToken.id;
          } catch (tokenError) {
            console.log('Token verification failed, continuing as guest');
          }
        } else if (req.cookies && req.cookies.guest_token) {
          // Extract guest ID from guest token
          try {
            const decodedGuestToken = jwt.verify(req.cookies.guest_token, process.env.JWT_SECRET);
            userIdToSave = decodedGuestToken.id;
          } catch (guestTokenError) {
            console.log('Guest token verification failed');
          }
        }

        await ChatConversation.findOneAndUpdate(
          { sessionId: currentSessionId },
          {
            sessionId: currentSessionId,
            'userInfo.ip': req.ip,
            'userInfo.userAgent': req.headers['user-agent'],
            'userInfo.userId': userIdToSave, // Add user ID to the conversation
            messages: chatSession.messages,
            lastActivity: new Date()
          },
          { upsert: true, new: true }
        );
      } catch (dbError) {
        console.error('Error saving conversation to database:', dbError);
        // Don't fail the request if database save fails
      }

      res.json(formattedResponse);
    } catch (error) {
      console.error('ChatbotController error:', error);
      res.status(500).json({ error: 'Internal Server Error', message: error.message });
    }
  },

  // Add a method to clear a session
  clearSession: async (req, res) => {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        return res.status(400).json({ error: 'Bad Request', message: 'Session ID is required' });
      }

      if (chatSessions.has(sessionId)) {
        chatSessions.delete(sessionId);
        return res.json({ success: true, message: 'Session cleared' });
      } else {
        return res.status(404).json({ error: 'Not Found', message: 'Session not found' });
      }
    } catch (error) {
      console.error('Error clearing session:', error);
      return res.status(500).json({ error: 'Internal Server Error', message: error.message });
    }
  }
};

module.exports = chatbotController;