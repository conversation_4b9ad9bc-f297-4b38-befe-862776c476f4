"use client"
import React, { useState, useEffect } from 'react';
import { Briefcase, Edit, Trash2, Plus, Eye, X, CheckCircle, XCircle, FileText, Mail, Phone, Download, Clock, AlertCircle } from 'lucide-react';

// Dummy data for job listings
const dummyJobs = [
  {
    id: '1',
    title: 'Software Engineer',
    department: 'Engineering',
    location: 'Remote',
    type: 'full-time',
    description: 'We are looking for a Software Engineer...',
    requirements: ['Experience with React', 'Experience with Node.js'],
    responsibilities: ['Develop new features', 'Fix bugs'],
    benefits: ['Health insurance', '401(k)'],
    salary_range: { min: 60000, max: 120000, currency: 'USD' },
    status: 'published',
    created_at: '2023-01-01T00:00:00Z'
  },
  {
    id: '2',
    title: 'Product Manager',
    department: 'Product',
    location: 'New York, NY',
    type: 'full-time',
    description: 'We are looking for a Product Manager...',
    requirements: ['Experience with product management', 'Strong communication skills'],
    responsibilities: ['Define product roadmap', 'Work with engineering team'],
    benefits: ['Health insurance', 'Stock options'],
    salary_range: { min: 80000, max: 150000, currency: 'USD' },
    status: 'draft',
    created_at: '2023-02-01T00:00:00Z'
  }
];

// Dummy data for job applications
const dummyApplications = {
  '1': [
    {
      id: '1',
      job_id: '1',
      full_name: 'John Doe',
      email: '<EMAIL>',
      phone: '************',
      resume_url: 'https://example.com/resume/john_doe.pdf',
      cover_letter: 'I am very interested in this position...',
      status: 'pending',
      created_at: '2023-01-10T00:00:00Z'
    }
  ],
  '2': []
};

function JobManagement() {
  const [jobs, setJobs] = useState([]);
  const [applications, setApplications] = useState({});
  const [selectedJob, setSelectedJob] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    department: '',
    location: '',
    type: 'full-time',
    description: '',
    requirements: [''],
    responsibilities: [''],
    benefits: [''],
    salary_range: null,
    status: 'draft'
  });

  useEffect(() => {
    fetchJobs();
  }, []);

  useEffect(() => {
    if (selectedJob) {
      fetchApplications(selectedJob);
    }
  }, [selectedJob]);

  const fetchJobs = async () => {
    try {
      // Simulate fetching data
      setJobs(dummyJobs);
      setApplications(dummyApplications);
    } catch (err) {
      setError('Failed to fetch jobs');
    } finally {
      setLoading(false);
    }
  };

  const fetchApplications = async (jobId) => {
    try {
      // Simulate fetching data
      setApplications(prev => ({
        ...prev,
        [jobId]: dummyApplications[jobId] || []
      }));
    } catch (err) {
      setError('Failed to fetch applications');
    }
  };

  const handleDownloadResume = async (application) => {
    try {
      // Simulate downloading resume
      const url = application.resume_url;
      const a = document.createElement('a');
      a.href = url;
      const extension = application.resume_url.split('.').pop() || 'pdf';
      a.download = `${application.full_name}_resume.${extension}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    } catch (err) {
      setError('Could not download resume. The file may have been moved or deleted.');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);

    try {
      const jobData = {
        ...formData,
        requirements: formData.requirements.filter(r => r.trim()),
        responsibilities: formData.responsibilities.filter(r => r.trim()),
        benefits: formData.benefits.filter(b => b.trim())
      };

      if (editingId) {
        // Simulate updating a job
        setJobs(jobs.map(job => job.id === editingId ? { ...job, ...jobData } : job));
      } else {
        // Simulate adding a new job
        const newJob = {
          ...jobData,
          id: (jobs.length + 1).toString(),
          created_at: new Date().toISOString()
        };
        setJobs([...jobs, newJob]);
      }

      setFormData({
        title: '',
        department: '',
        location: '',
        type: 'full-time',
        description: '',
        requirements: [''],
        responsibilities: [''],
        benefits: [''],
        salary_range: null,
        status: 'draft'
      });
      setShowForm(false);
      setEditingId(null);
    } catch (err) {
      setError(err.message);
    }
  };

  const handleEdit = (job) => {
    setFormData({
      title: job.title,
      department: job.department,
      location: job.location,
      type: job.type,
      description: job.description,
      requirements: job.requirements,
      responsibilities: job.responsibilities,
      benefits: job.benefits,
      salary_range: job.salary_range,
      status: job.status
    });
    setEditingId(job.id);
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    if (!window.confirm('Are you sure you want to delete this job listing?')) return;

    try {
      // Simulate deleting a job
      setJobs(jobs.filter(job => job.id !== id));
    } catch (err) {
      setError('Failed to delete job');
    }
  };

  const handleArrayInput = (field, index, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }));
  };

  const addArrayItem = (field) => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }));
  };

  const removeArrayItem = (field, index) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  const toggleStatus = async (id, currentStatus) => {
    try {
      const newStatus = currentStatus === 'published' ? 'draft' : 'published';
      // Simulate updating job status
      setJobs(jobs.map(job => job.id === id ? { ...job, status: newStatus } : job));
    } catch (err) {
      setError('Failed to update job status');
    }
  };

  const updateApplicationStatus = async (applicationId, newStatus) => {
    try {
      // Simulate updating application status
      setApplications(prev => ({
        ...prev,
        [selectedJob]: prev[selectedJob].map(app => app.id === applicationId ? { ...app, status: newStatus } : app)
      }));
    } catch (err) {
      setError('Failed to update application status');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Job Listings</h2>
        <button
          onClick={() => {
            setShowForm(true);
            setEditingId(null);
            setFormData({
              title: '',
              department: '',
              location: '',
              type: 'full-time',
              description: '',
              requirements: [''],
              responsibilities: [''],
              benefits: [''],
              salary_range: null,
              status: 'draft'
            });
          }}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add Job Listing
        </button>
      </div>

      {error && (
        <div className="bg-red-50 text-red-600 p-4 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          {error}
        </div>
      )}

      {showForm && (
        <div className="bg-white p-6 rounded-lg shadow-lg">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-xl font-bold">
              {editingId ? 'Edit Job Listing' : 'Add New Job Listing'}
            </h3>
            <button
              onClick={() => {
                setShowForm(false);
                setEditingId(null);
              }}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Job Title
                </label>
                <input
                  type="text"
                  required
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Department
                </label>
                <input
                  type="text"
                  required
                  value={formData.department}
                  onChange={(e) => setFormData({ ...formData, department: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Location
                </label>
                <input
                  type="text"
                  required
                  value={formData.location}
                  onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Employment Type
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="full-time">Full Time</option>
                  <option value="part-time">Part Time</option>
                  <option value="contract">Contract</option>
                  <option value="internship">Internship</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                required
                rows={4}
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Requirements
              </label>
              {formData.requirements.map((req, index) => (
                <div key={`req-${index}`} className="flex items-center space-x-2 mb-2">
                  <input
                    type="text"
                    value={req}
                    onChange={(e) => handleArrayInput('requirements', index, e.target.value)}
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button
                    type="button"
                    onClick={() => removeArrayItem('requirements', index)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
              ))}
              <button
                type="button"
                onClick={() => addArrayItem('requirements')}
                className="text-blue-600 hover:text-blue-700 font-medium"
              >
                Add Requirement
              </button>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Responsibilities
              </label>
              {formData.responsibilities.map((resp, index) => (
                <div key={`resp-${index}`} className="flex items-center space-x-2 mb-2">
                  <input
                    type="text"
                    value={resp}
                    onChange={(e) => handleArrayInput('responsibilities', index, e.target.value)}
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button
                    type="button"
                    onClick={() => removeArrayItem('responsibilities', index)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
              ))}
              <button
                type="button"
                onClick={() => addArrayItem('responsibilities')}
                className="text-blue-600 hover:text-blue-700 font-medium"
              >
                Add Responsibility
              </button>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Benefits
              </label>
              {formData.benefits.map((benefit, index) => (
                <div key={`benefit-${index}`} className="flex items-center space-x-2 mb-2">
                  <input
                    type="text"
                    value={benefit}
                    onChange={(e) => handleArrayInput('benefits', index, e.target.value)}
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button
                    type="button"
                    onClick={() => removeArrayItem('benefits', index)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
              ))}
              <button
                type="button"
                onClick={() => addArrayItem('benefits')}
                className="text-blue-600 hover:text-blue-700 font-medium"
              >
                Add Benefit
              </button>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Salary Range (Optional)
              </label>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <input
                    type="number"
                    placeholder="Min"
                    value={formData.salary_range?.min || ''}
                    onChange={(e) => setFormData({
                      ...formData,
                      salary_range: {
                        ...formData.salary_range,
                        min: parseInt(e.target.value),
                        currency: formData.salary_range?.currency || 'USD'
                      }
                    })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <input
                    type="number"
                    placeholder="Max"
                    value={formData.salary_range?.max || ''}
                    onChange={(e) => setFormData({
                      ...formData,
                      salary_range: {
                        ...formData.salary_range,
                        max: parseInt(e.target.value),
                        currency: formData.salary_range?.currency || 'USD'
                      }
                    })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <select
                    value={formData.salary_range?.currency || 'USD'}
                    onChange={(e) => setFormData({
                      ...formData,
                      salary_range: {
                        ...formData.salary_range,
                        currency: e.target.value
                      }
                    })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                  </select>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="draft">Draft</option>
                <option value="published">Published</option>
                <option value="archived">Archived</option>
              </select>
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => {
                  setShowForm(false);
                  setEditingId(null);
                }}
                className="text-gray-600 hover:text-gray-900"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700"
              >
                {editingId ? 'Update Job Listing' : 'Create Job Listing'}
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Job Title
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Department
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Location
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Applications
              </th>
              <th className="px-6 py-3 bg-gray-50"></th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {jobs.map((job) => (
              <React.Fragment key={job.id}>
                <tr className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Briefcase className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {job.title}
                        </div>
                        <div className="text-sm text-gray-500">
                          {job.type}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {job.department}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {job.location}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => toggleStatus(job.id, job.status)}
                      className={`px-3 py-1 rounded-full text-xs font-semibold ${job.status === 'published'
                          ? 'bg-green-100 text-green-800'
                          : job.status === 'archived'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}
                    >
                      {job.status}
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => setSelectedJob(selectedJob === job.id ? null : job.id)}
                      className="flex items-center text-blue-600 hover:text-blue-700"
                    >
                      <FileText className="h-5 w-5 mr-2" />
                      {applications[job.id]?.length || 0} Applications
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => handleEdit(job)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Edit className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDelete(job.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </div>
                  </td>
                </tr>
                {selectedJob === job.id && (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 bg-gray-50">
                      <div className="space-y-4">
                        <h4 className="font-semibold text-lg">Applications</h4>
                        {applications[job.id]?.length > 0 ? (
                          <div className="space-y-4">
                            {applications[job.id].map((application) => (
                              <div key={application.id} className="bg-white p-4 rounded-lg shadow">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <h5 className="font-semibold">{application.full_name}</h5>
                                    <div className="space-y-1 mt-2 text-sm text-gray-600">
                                      <div className="flex items-center">
                                        <Mail className="h-4 w-4 mr-2" />
                                        <a href={`mailto:${application.email}`} className="hover:text-blue-600">
                                          {application.email}
                                        </a>
                                      </div>
                                      <div className="flex items-center">
                                        <Phone className="h-4 w-4 mr-2" />
                                        <span>{application.phone}</span>
                                      </div>
                                      <div className="flex items-center">
                                        <Clock className="h-4 w-4 mr-2" />
                                        <span>{new Date(application.created_at).toLocaleDateString()}</span>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-4">
                                    <button
                                      onClick={() => handleDownloadResume(application)}
                                      className="flex items-center text-blue-600 hover:text-blue-700"
                                    >
                                      <Download className="h-5 w-5 mr-1" />
                                      Resume
                                    </button>
                                    <select
                                      value={application.status}
                                      onChange={(e) => updateApplicationStatus(application.id, e.target.value)}
                                      className="px-3 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                      <option value="pending">Pending</option>
                                      <option value="reviewed">Reviewed</option>
                                      <option value="interviewing">Interviewing</option>
                                      <option value="accepted">Accepted</option>
                                      <option value="rejected">Rejected</option>
                                    </select>
                                  </div>
                                </div>
                                {application.cover_letter && (
                                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                                    <h6 className="font-medium mb-2">Cover Letter</h6>
                                    <p className="text-gray-600 whitespace-pre-wrap">{application.cover_letter}</p>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-gray-600">No applications received yet.</p>
                        )}
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default JobManagement;