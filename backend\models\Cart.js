const Package = require("../models/Package");
const mongoose = require("mongoose");
const Schema = mongoose.Schema;

// Cart Schema
const cartSchema = new Schema(
  {
    user: {
      type: Schema.Types.Mixed, // Changed from ObjectId to Mixed to support both ObjectId and string
      ref: "User",
      required: true,
    },
    isGuest: {
      type: Boolean,
      default: function () {
        return typeof this.user === "string" && this.user.startsWith("guest-");
      },
    },
    items: [
      {
        package: {
          type: Schema.Types.ObjectId,
          ref: "Package",
          required: function () {
            return this.type !== "domain"; // Only required if not a domain
          },
        },
        quantity: {
          type: Number,
          required: true,
          min: 1,
        },
        price: {
          type: Number,
          required: true,
        },
        discount: {
          type: Number,
          required: true,
          default: 0,
        },
        period: {
          type: Number,
          required: true,
          default: 1,
        },
        // New fields for domain items
        type: {
          type: String,
          enum: ["package", "domain"],
          default: "package",
        },
        domainName: {
          type: String,
          required: function () {
            return this.type === "domain";
          },
        },
        tld: {
          type: String,
          required: function () {
            return this.type === "domain";
          },
        },
        // Raw pricing data for domains to support dynamic period selection
        rawPricing: {
          type: Schema.Types.Mixed,
          required: false,
          default: null,
        },
      },
    ],
    totalPrice: {
      type: Number,
      required: true,
      default: 0,
    },
    totalDiscount: {
      type: Number,
      required: true,
      default: 0,
    },
    cartCount: {
      type: Number,
      required: true,
      default: 0,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Pre-save hook to update total price and total discount when the cart items change
cartSchema.pre("save", function (next) {
  this.totalPrice = this.items.reduce((acc, item) => {
    if (item.type === "domain") {
      // For domain items, just add the price (no quantity multiplication)
      return acc + item.price - item.discount;
    } else {
      // For package items, use the existing calculation
      return acc + item.price * item.quantity * item.period - item.discount;
    }
  }, 0);

  this.totalDiscount = this.items.reduce((acc, item) => acc + item.discount, 0);

  // Count only package items for cartCount
  this.cartCount = this.items.reduce((acc, item) => {
    if (item.type === "domain") {
      return acc + 1; // Count each domain as 1 item
    } else {
      return acc + item.quantity;
    }
  }, 0);

  this.updatedAt = Date.now();
  next();
});

// Method to add item to the cart
cartSchema.methods.addItem = async function (package, quantity = 1, period) {
  // discount
  // price,
  try {
    if (!package) {
      throw { message: "Package not found", code: "PACKAGE_NOT_FOUND" };
    }

    const existingItemIndex = this.items.findIndex(
      (item) => item.package?._id.toString() === package._id.toString()
    );

    if (existingItemIndex > -1) {
      // Item already in cart, update quantity
      if (this.items[existingItemIndex].quantity + quantity > 10) {
        throw {
          message: "errors.max_quantity_error",
          code: "MAX_QUANTITY_REACHED",
        };
      }

      this.items[existingItemIndex].quantity += quantity;
      this.items[existingItemIndex].price = package.price; // Update price

      // Find the correct discount based on the period
      console.log("period: ", this.items[existingItemIndex].period);
      const discountEntry = package.discounts.find(
        (d) => d.period === this.items[existingItemIndex].period && d.period > 0
      );
      console.log("discountEntryeee: ", discountEntry?.percentage);

      const discountPercentage = discountEntry ? discountEntry.percentage : 0;

      this.items[existingItemIndex].discount = this.calculateItemDiscount(
        this.items[existingItemIndex],
        package,
        discountPercentage
      );
    } else {
      // Item not in cart, add new item
      this.items.push({
        package: package._id,
        quantity,
        price: package.price,
        period,
      });

      const newItemIndex = this.items.length - 1;
      this.items[newItemIndex].price = package.price;

      // Find the correct discount based on the period
      const discountEntry = package.discounts.find(
        (d) => d.period === period && d.period > 0
      );
      console.log("discountEntryeee  new: ", discountEntry?.percentage);
      const discountPercentage = discountEntry ? discountEntry.percentage : 0;

      this.items[newItemIndex].discount = this.calculateItemDiscount(
        this.items[newItemIndex],
        package,
        discountPercentage
      );
    }

    return this.save();
  } catch (error) {
    throw error;
  }
};

cartSchema.methods.removeItem = async function (packageId, quantity = 1) {
  const package = await Package.findById(packageId);
  const existingItemIndex = this.items.findIndex(
    (item) => item.package?._id.toString() === packageId.toString()
  );

  if (existingItemIndex > -1) {
    // Item found in cart
    this.items[existingItemIndex].quantity -= quantity;
    this.items[existingItemIndex].price = package.price;

    const discountEntry = package.discounts.find(
      (d) => d.period === this.items[existingItemIndex].period && d.period > 0
    );
    const discountPercentage = discountEntry ? discountEntry.percentage : 0;

    this.items[existingItemIndex].discount = this.calculateItemDiscount(
      this.items[existingItemIndex],
      package,
      discountPercentage
    ); // Recalculate discount

    if (this.items[existingItemIndex].quantity <= 0) {
      // Remove item if quantity becomes zero or less
      this.items.splice(existingItemIndex, 1);
    }
  }
  return this.save();
};

// Method to clear the cart
cartSchema.methods.clearCart = function () {
  this.items = [];
  this.totalPrice = 0;
  return this.save();
};

cartSchema.methods.calculateItemDiscount = function (
  item,
  package,
  discountRate
) {
  return (discountRate / 100) * (package.price * item.quantity * item.period);
};

// Method to add domain to the cart
cartSchema.methods.addDomain = async function (
  domainName,
  tld,
  price,
  period = 1,
  rawPricing = null
) {
  try {
    // Check if this domain is already in the cart
    const existingItemIndex = this.items.findIndex(
      (item) => item.type === "domain" && item.domainName === domainName
    );

    if (existingItemIndex > -1) {
      // Domain already in cart, update period and price if needed
      this.items[existingItemIndex].period = period;
      this.items[existingItemIndex].price = price;
      // Always update rawPricing, even if it's null
      this.items[existingItemIndex].rawPricing = rawPricing;
    } else {
      // Domain not in cart, add new item
      this.items.push({
        type: "domain",
        domainName,
        tld,
        quantity: 1, // Always 1 for domains
        price,
        period,
        discount: 0, // No discounts for domains initially
        rawPricing: rawPricing,
      });
    }

    return this.save();
  } catch (error) {
    throw error;
  }
};

const Cart = mongoose.model("Cart", cartSchema);

module.exports = Cart;
