import React, { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import ChatMessage from './ChatMessage';
import ConfirmDialog from './ConfirmDialog';
import ChatRecommendations from './ChatRecommendations';
import ChatbotService from '../../app/services/chatbotService';
import './Chatbot.css';

const ChatbotWindow = ({ isOpen, onClose, onMinimize }) => {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [windowHeight, setWindowHeight] = useState(420); // Increased height to accommodate recommendations
  const [showClearConfirm, setShowClearConfirm] = useState(false);
  const [showRecommendations, setShowRecommendations] = useState(true);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const messageContainerRef = useRef(null);
  const latestBotMessageRef = useRef(null);

  // Function to prevent scroll propagation
  const handleWheel = useCallback((e) => {
    const container = messageContainerRef.current;
    if (!container) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const isAtTop = scrollTop === 0;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 1;

    // If we're at the top and trying to scroll up, or at the bottom and trying to scroll down
    if ((isAtTop && e.deltaY < 0) || (isAtBottom && e.deltaY > 0)) {
      e.preventDefault();
    }
  }, []);

  // Function to handle touch events for mobile
  const handleTouchMove = useCallback((e) => {
    const container = messageContainerRef.current;
    if (!container) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const isAtTop = scrollTop <= 0;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 1;

    // Get touch direction (positive = down, negative = up)
    const touch = e.touches[0];
    const startY = container.touchStartY || touch.clientY;
    const touchDirection = touch.clientY - startY;

    // If we're at the top and trying to scroll up, or at the bottom and trying to scroll down
    if ((isAtTop && touchDirection > 0) || (isAtBottom && touchDirection < 0)) {
      e.preventDefault();
    }
  }, []);

  // Calculate appropriate height based on content
  const updateWindowHeight = () => {
    // Use a larger minimum height when showing recommendations
    const minHeight = showRecommendations ? 420 : 350;
    const maxHeight = 500; // Increased maximum height

    // Calculate ideal height based on message count with some padding
    const idealHeight = Math.min(maxHeight, Math.max(minHeight, 250 + (messages.length * 50)));
    setWindowHeight(idealHeight);
  };

  // Scroll to the latest bot message or bottom of messages
  const scrollToLatestMessage = () => {
    // If we have a reference to the latest bot message, scroll to it
    if (latestBotMessageRef.current) {
      latestBotMessageRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start' // Align to the top of the viewport
      });
    }
    // Fallback to scrolling to bottom if no bot message ref is available
    else if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Focus input when chat window opens
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        // Focus the input field
        if (inputRef.current) {
          inputRef.current.focus();
        }
        // Also scroll to latest message when opening chat
        scrollToLatestMessage();
      }, 300); // Small delay to ensure DOM is ready
    }
  }, [isOpen]);

  // Initialize chatbot with saved messages or welcome message - only run once on component mount
  useEffect(() => {
    // Try to load saved messages from localStorage
    const savedMessages = ChatbotService.getSavedMessages();

    if (savedMessages && savedMessages.length > 0) {
      setMessages(savedMessages);
    } else {
      // If no saved messages, show welcome message
      setMessages([{ role: 'assistant', content: "Hello! I'm the ZTech Engineering virtual assistant. How can I help you today?" }]);
    }
  }, []); // Empty dependency array means this runs once on mount

  // Save messages before page unload to ensure persistence
  useEffect(() => {
    // Add an event listener to save messages before the page unloads
    const handleBeforeUnload = () => {
      if (messages.length > 0) {
        ChatbotService.saveMessages(messages);
      }
    };

    // Add the event listener
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', handleBeforeUnload);
    }

    return () => {
      // Remove the event listener and keep the session
      if (typeof window !== 'undefined') {
        window.removeEventListener('beforeunload', handleBeforeUnload);
      }
    };
  }, [messages]);

  // Update height and scroll to latest message when messages or recommendations change
  useEffect(() => {
    updateWindowHeight();
    scrollToLatestMessage();
  }, [messages, showRecommendations]);

  // Focus on input after each message is received (not just when chat opens)
  useEffect(() => {
    // Only refocus if not loading (meaning a response was received)
    if (!isLoading && inputRef.current && messages.length > 0) {
      inputRef.current.focus();
    }
  }, [isLoading, messages.length]);

  // Add wheel and touch event listeners to prevent scroll propagation
  useEffect(() => {
    const container = messageContainerRef.current;
    if (!container) return;

    // Store touch start position
    const handleTouchStart = (e) => {
      container.touchStartY = e.touches[0].clientY;
    };

    // Use passive: false to allow preventDefault()
    container.addEventListener('wheel', handleWheel, { passive: false });
    container.addEventListener('touchstart', handleTouchStart, { passive: true });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });

    return () => {
      container.removeEventListener('wheel', handleWheel);
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
    };
  }, [handleWheel, handleTouchMove]);

  const handleInputChange = (e) => {
    setInput(e.target.value);
  };

  const sendMessage = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage = input.trim();
    setInput('');
    setIsLoading(true);
    setError(null);
    setShowRecommendations(false);

    // Add user message to chat
    const userMsg = { role: 'user', content: userMessage };
    const updatedMessages = [...messages, userMsg];
    setMessages(updatedMessages);

    // Save messages to localStorage immediately after user sends a message
    ChatbotService.saveMessages(updatedMessages);

    try {
      // Send all messages to maintain conversation context
      const response = await ChatbotService.sendMessage(updatedMessages);

      if (response.choices && response.choices.length > 0) {
        const botResponse = response.choices[0].message;
        const newMessages = [...updatedMessages, botResponse];
        setMessages(newMessages);

        // Save messages to localStorage
        ChatbotService.saveMessages(newMessages);
      } else {
        throw new Error('Invalid response from chatbot');
      }
    } catch (err) {
      console.error('Chatbot error:', err);
      setError("Sorry, I encountered an error. Please try again or contact support.");
      const errorMessages = [...updatedMessages, {
        role: 'assistant',
        content: "Sorry, I encountered an error. Please try again or contact our support team."
      }];
      setMessages(errorMessages);

      // Save error messages to localStorage
      ChatbotService.saveMessages(errorMessages);
    } finally {
      setIsLoading(false);
      // No need to focus here as the useEffect will handle it after isLoading changes
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault(); // Prevent new line
      sendMessage();
    }
  };

  const handleClearChat = async () => {
    try {
      await ChatbotService.clearSession();
      const newMessages = [{ role: 'assistant', content: "Chat history cleared. How can I help you today?" }];
      setMessages(newMessages);
      ChatbotService.saveMessages(newMessages);
      setShowRecommendations(true);
    } catch (error) {
      console.error('Error clearing chat:', error);
    } finally {
      setShowClearConfirm(false);
    }
  };

  const handleSelectRecommendation = (recommendation) => {
    setInput(recommendation);
    setShowRecommendations(false);
  };

  if (!isOpen) return null;

  return (
    <>
      <div
        className="absolute bottom-[58px] right-0 z-[1001] w-[300px] sm:w-[320px] bg-white rounded-xl shadow-xl border border-gray-100 flex flex-col overflow-hidden transition-all duration-300"
        style={{
          height: `${windowHeight}px`,
          boxShadow: '0 10px 25px rgba(73, 126, 247, 0.15)'
        }}
      >
      <div className="bg-[#497ef7] text-white px-4 py-2.5 flex justify-between items-center">
        <div className="flex items-center">
          <h3 className="font-medium text-sm">ZTech Assistant</h3>
        </div>

        <div className="flex items-center gap-1">
          {/* Clear chat button */}
          <button
            onClick={() => setShowClearConfirm(true)}
            className="text-xs bg-white/20 hover:bg-white/30 rounded px-2 py-1 flex items-center justify-center transition-colors"
            aria-label="Clear chat"
          >
            Clear Chat
          </button>

          {/* Close button */}
          <button
            onClick={onClose}
            className="w-6 h-6 hover:bg-white/20 rounded-full flex items-center justify-center transition-colors"
            aria-label="Close"
          >
            <span className="text-lg">×</span>
          </button>
        </div>
      </div>

      <div
        ref={messageContainerRef}
        className="flex-1 overflow-y-auto p-3 flex flex-col gap-2.5 modern-scrollbar chatbot-bg-pattern prevent-scroll-chaining"
        style={{
          maxHeight: `${windowHeight - 100}px`,
          background: 'linear-gradient(to bottom, #f8faff, #f0f4ff)',
          scrollbarWidth: 'thin',
          scrollbarColor: 'rgba(73, 126, 247, 0.2) rgba(240, 244, 255, 0.5)'
        }}
      >
        {messages
          .filter(msg => msg.role !== 'system') // Don't show system messages to user
          .map((msg, index, array) => {
            // Check if this is the latest bot message
            const isLatestBotMessage =
              msg.role === 'assistant' &&
              index === array.filter(m => m.role !== 'system').length - 1 &&
              array[array.length - 1].role === 'assistant';

            return (
              <ChatMessage
                key={index}
                message={msg.content}
                isUser={msg.role === 'user'}
                ref={isLatestBotMessage ? latestBotMessageRef : null}
              />
            );
          })
        }

        {/* Show recommendations if there are no user messages or only the welcome message */}
        {showRecommendations && messages.filter(msg => msg.role === 'user').length === 0 && (
          <ChatRecommendations onSelectRecommendation={handleSelectRecommendation} />
        )}

        {isLoading && (
          <div className="w-full py-1 px-1">
            <div className="flex gap-1 items-center">
              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0ms'}}></div>
              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '150ms'}}></div>
              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '300ms'}}></div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      <div
        className="border-t border-gray-100 px-3 py-2.5"
        style={{
          background: 'linear-gradient(to bottom, #f8faff, #f0f4ff)'
        }}
      >
        <div className="flex items-center gap-2">
          <input
            ref={inputRef}
            type="text"
            value={input}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Type your message..."
            disabled={isLoading}
            className="flex-1 border border-gray-200 rounded-full py-1.5 px-3 text-sm focus:outline-none focus:ring-2 focus:ring-[#497ef7]/40"
          />

          <button
            onClick={sendMessage}
            disabled={isLoading || !input.trim()}
            className={`p-1.5 rounded-full ${input.trim() ? 'bg-[#497ef7] text-white' : 'bg-gray-100 text-gray-400'} transition-colors`}
            aria-label="Send message"
          >
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M22 2L11 13M22 2L15 22L11 13M11 13L2 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
      </div>

      <div
        className="text-center text-xs text-gray-500 py-1 border-t border-gray-100"
        style={{
          background: 'linear-gradient(to bottom, #f8faff, #f0f4ff)'
        }}
      >
        Powered by ZTech AI
      </div>
    </div>

    {/* Confirmation Dialog */}
    <ConfirmDialog
      isOpen={showClearConfirm}
      message="Are you sure you want to clear the chat history?"
      onConfirm={handleClearChat}
      onCancel={() => setShowClearConfirm(false)}
    />
    </>
  );
};

ChatbotWindow.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onMinimize: PropTypes.func.isRequired
};

export default ChatbotWindow;