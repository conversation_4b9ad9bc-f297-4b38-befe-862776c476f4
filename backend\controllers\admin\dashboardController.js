const User = require("../../models/User");
const Order = require("../../models/Order");
const Package = require("../../models/Package");
const Payment = require("../../models/Payment");
const Ticket = require("../../models/Ticket");
const adminLogger = require("../../utils/adminLogger");
const Category = require("../../models/Category");
const Brand = require("../../models/Brand");

/**
 * Get dashboard statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDashboardStats = async (req, res) => {
  try {
    // Get total users count
    let totalUsers = 0;
    try {
      totalUsers = await User.countDocuments();
    } catch (err) {
      console.error("Error counting users:", err);
    }

    // Get total packages count
    let totalPackages = 0;
    try {
      totalPackages = await Package.countDocuments();
    } catch (err) {
      console.error("Error counting packages:", err);
    }

    // Get total orders count
    let totalOrders = 0;
    try {
      totalOrders = await Order.countDocuments();
    } catch (err) {
      console.error("Error counting orders:", err);
    }

    // Get total revenue from completed orders
    let totalRevenue = 0;
    try {
      // Since Payment model doesn't have an amount field, we need to get the totalPrice from the Order
      const completedPayments = await Payment.find({
        status: "completed",
      }).populate("order", "totalPrice");

      // Calculate revenue from order totalPrice
      totalRevenue = completedPayments.reduce((sum, payment) => {
        // Check if order exists and has totalPrice
        if (payment.order && payment.order.totalPrice) {
          return sum + payment.order.totalPrice;
        }
        return sum;
      }, 0);
    } catch (err) {
      console.error("Error calculating revenue:", err);
    }

    // Get recent orders (last 5)
    let formattedRecentOrders = [];
    try {
      const recentOrders = await Order.find()
        .sort({ createdAt: -1 })
        .limit(5)
        .populate("user", "firstName lastName email")
        .lean();

      // Format recent orders for display
      formattedRecentOrders = recentOrders.map((order) => ({
        id: order._id,
        identifiant: order.identifiant,
        customer: order.user
          ? `${order.user.firstName} ${order.user.lastName}`
          : "Unknown",
        amount: order.totalPrice || 0,
        status: order.status || "UNKNOWN",
        date: order.createdAt
          ? order.createdAt.toISOString().split("T")[0]
          : "Unknown",
      }));
    } catch (err) {
      console.error("Error fetching recent orders:", err);
    }

    // Get recent support tickets (last 5)
    let recentTickets = [];
    try {
      const tickets = await Ticket.find()
        .sort({ createdAt: -1 })
        .limit(5)
        .populate("creator", "firstName lastName email")
        .lean();

      // Format recent tickets for display
      recentTickets = tickets.map((ticket) => ({
        id: ticket._id,
        identifiant: ticket.identifiant,
        subject: ticket.subject,
        customer: ticket.creator
          ? `${ticket.creator.firstName} ${ticket.creator.lastName}`
          : "Unknown",
        priority: ticket.priority,
        status: ticket.status,
        date: ticket.createdAt
          ? ticket.createdAt.toISOString().split("T")[0]
          : "Unknown",
      }));
    } catch (err) {
      console.error("Error fetching recent tickets:", err);
    }

    // Return all statistics
    // Get monthly revenue for the current year
    let monthlyRevenue = Array(12).fill(0);
    let weeklyRevenue = Array(7).fill(0); // Sunday to Saturday
    let yearlyRevenue = {}; // {year: total}
    try {
      const now = new Date();
      const currentYear = now.getFullYear();
      const completedPayments = await Payment.find({
        status: "completed",
      }).populate("order", "totalPrice createdAt");

      completedPayments.forEach((payment) => {
        if (
          payment.order &&
          payment.order.totalPrice &&
          payment.order.createdAt
        ) {
          const date = new Date(payment.order.createdAt);

          // Monthly revenue
          if (date.getFullYear() === currentYear) {
            const month = date.getMonth(); // 0 = Jan, 11 = Dec
            monthlyRevenue[month] += payment.order.totalPrice;
          }

          // Weekly revenue (last 7 days)
          const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));
          if (diffDays < 7) {
            // 0 = today, 6 = 6 days ago
            const weekDay = 6 - diffDays; // so index 6 is today, 0 is 6 days ago
            if (weekDay >= 0 && weekDay < 7) {
              weeklyRevenue[weekDay] += payment.order.totalPrice;
            }
          }

          // Yearly revenue
          const year = date.getFullYear();
          if (!yearlyRevenue[year]) yearlyRevenue[year] = 0;
          yearlyRevenue[year] += payment.order.totalPrice;
        }
      });
    } catch (err) {
      console.error("Error calculating revenues:", err);
    }

    const responseData = {
      success: true,
      data: {
        users: totalUsers,
        packages: totalPackages,
        orders: totalOrders,
        revenue: totalRevenue,
        monthlyRevenue,
        weeklyRevenue,
        yearlyRevenue,
        recentOrders: formattedRecentOrders,
        recentTickets: recentTickets,
      },
    };

    return res.status(200).json(responseData);
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    return res.status(500).json({
      success: false,
      message: "Error fetching dashboard statistics",
      error: error.message,
    });
  }
};

// Get package distribution by category
exports.getPackageDistributionByCategory = async (req, res) => {
  try {
    const categoryId = req.query.categoryId;
    if (!categoryId) {
      return res.status(400).json({ success: false, message: "Category ID is required" });
    }

    // Find all brands in the category
    const brands = await Brand.find({ category: categoryId }).lean();
    // console.log(brands);

    const completedPayments = await Payment.find({
      status: "completed",
    }).populate({
      path: 'order',
      populate: {
        path: 'subOrders'
      }
    })
    // console.log(completedPayments);

    // Map package IDs to brand IDs
    const packageIdToBrandId = {};
    brands.forEach(brand => {
      brand.packages.forEach(pkgId => {
        packageIdToBrandId[pkgId.toString()] = brand._id.toString();
      });
    });

    const brandSales = {};
    const packageSales = {};
    brands.forEach(brand => {
      brandSales[brand._id.toString()] = 0;
      brand.packages.forEach(pkgId => {
        packageSales[pkgId.toString()] = 0;
      });
    });
    completedPayments.forEach(payment => {
      if (payment.order && payment.order.subOrders) {
        payment.order.subOrders.forEach(subOrder => {
          const pkgId = subOrder.package?.toString();
          const brandId = packageIdToBrandId[pkgId];
          if (brandId && brandSales.hasOwnProperty(brandId)) {
            const quantity = subOrder.quantity || 0;
            brandSales[brandId] += quantity;
            packageSales[pkgId] += quantity;
          }
        });
      }
    });


    // For each brand, count the number of packages
    const brandData = await Promise.all(
      brands.map(async (brand) => {
        const packages = await Package.find({ brand: brand._id });
        const packageCount = packages.length;
        return {
          brandId: brand._id,
          brandName: brand.name,
          sales: brandSales[brand._id.toString()] || 0,
          packageCount,
          packages: packages.map(pkg => ({
            packageId: pkg._id,
            name: pkg.name,
            sales: packageSales[pkg._id.toString()] || 0
          })),
        };
      })
    );

    res.status(200).json({
      success: true,
      data: brandData,
    });
  } catch (error) {
    console.error("Error fetching package distribution:", error);
    res.status(500).json({ success: false, message: "Server error" });
  }
};
