"use client";
import React, { useEffect, useRef, useState } from "react";
import { But<PERSON> } from "@material-tailwind/react";
import { UserCircleIcon, TrashIcon } from "@heroicons/react/24/solid";
import { usePathname, useRouter } from "next/navigation";
import { useAuth } from "../../app/context/AuthContext";
import cartService from "../../app/services/cartService";
import { AccountState } from "../../app/config/AccountState";
import { useTranslations } from "next-intl";
import {
  LayoutIcon,
  ServerIcon,
  ShieldIcon,
  ShoppingCart,
  SquareChevronRight,
} from "lucide-react";
import { AvatarIcon } from "../avatar/AvatarIcon";

// Mapping of category names to their corresponding icon components.
const categoryIcons = {
  Hosting: ServerIcon,
  SSL: ShieldIcon,
  Promotions: LayoutIcon,
};

// A small wrapper for the icon container.
const IconWrapper = ({ children }) => (
  <div className="h-12 w-12 flex-shrink-0 bg-blue-100 rounded-lg flex items-center justify-center">
    {children}
  </div>
);

export function AvatarWithUserDropdown() {
  const t = useTranslations("client");
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isCartMenuOpen, setIsCartMenuOpen] = useState(false);
  const { user, logout, cartCount, setCartCount } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isClient, setIsClient] = useState(false);
  const [cartData, setCartData] = useState({});
  const [imgError, setImgError] = useState(false);

  const cartRef = useRef(null);

  useEffect(() => {
    setIsClient(true);

    const fetchCartData = async () => {
      try {
        const cartData = await cartService.getCart();
        if (cartData.data.success) {
          setCartCount(cartData.data.cart.cartCount);
          setCartData(cartData.data.cart);
          console.log("pathname: ", pathname);
          if (
            !pathname.includes("/client/") &&
            !pathname.includes("/auth/") &&
            cartCount > 0
          ) {
            setIsCartMenuOpen(true);
          }
          // console.log('cartData.data.cart.items   = ', cartData.data.cart);
        } else {
          setCartCount(0);
        }
      } catch (error) {
        console.error("Error fetching cart data", error);
      }
    };

    fetchCartData();
  }, [cartCount]);

  // Close the dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (cartRef.current && !cartRef.current.contains(event.target)) {
        setIsCartMenuOpen(false);
      }
    }

    // Add event listener when cart is open
    if (isCartMenuOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isCartMenuOpen]);

  const handleLogout = async () => {
    await logout();
    closeMenu();
    window.location.href = "/auth/login";
  };

  const navigate = (toPage) => {
    router.push("/auth/" + toPage);
  };

  const handleQuantityChange = async (itemId, change, quantity) => {
    // Enforce boundary conditions immediately.
    let newQuantity =
      cartData?.items?.find((item) => item.package._id === itemId)?.quantity +
      change;

    if (quantity == "delete") {
      quantity = 1; // to ensure the quantity will get deleted after as we have identified the delete function specially on quantity = 1
      newQuantity = 5; // just to be sure that the condition below will be false
    }

    if (newQuantity < 1 || newQuantity > 10) {
      return;
    }

    // console.log('Change quantity of item with ID:', itemId, ' by ', change);
    try {
      let res;
      if (change > 0) {
        // Increase quantity
        res = await cartService.addItemToCart({ packageId: itemId, quantity });
        console.log("addItemToCart......", res);
      } else if (change < 0) {
        // Decrease quantity
        res = await cartService.removeItemFromCart({
          packageId: itemId,
          quantity,
        });
        console.log("removeItemFromCart......", res);
      } else {
        return;
      }

      // Update the state after the change
      setCartCount(res.data.cartCount);
      setCartData(res.data);
    } catch (error) {
      console.error("Error updating cart", error);
    }
  };

  const closeMenu = () => setIsMenuOpen(false);

  if (!isClient) {
    return null;
  }

  return (
    <div
      ref={cartRef}
      className="flex space-x-6 justify-center h-full items-center p-0 flex-shrink-0 max-w-[140px] min-w-[110px]"
    >
      <div
        className="relative font-poppins"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Cart Button */}
        <button
        name="Cart"
          className="relative flex items-center rounded-full p-0"
          onClick={() => setIsCartMenuOpen(!isCartMenuOpen)}
        >
          <div className="absolute -top-2 -right-3">
            <span className="bg-red-500 text-white text-xs font-bold px-1 rounded-full">
              {cartCount || 0}
            </span>
          </div>
          <ShoppingCart className="text-secondary w-6 h-6" />
        </button>

        {/* Cart Dropdown */}
        {isCartMenuOpen && (
          <div
            className="absolute sm:-right-9 -right-[80px] xlg:top-[36px] lg:top-[37px] top-[45px] p-4 w-[350px] sm:w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="font-semibold px-4 py-2 border-b border-gray-300 text-gray-800">
              <p className="font-poppins text-lg">{t("cart")}</p>
            </div>

            {/* Scrollable Items Section */}
            <div className="max-h-64 overflow-y-auto">
              {cartCount === 0 ? (
                <div className="flex items-center justify-center h-40">
                  <p className="text-center text-gray-500">
                    {t("no_items_in_cart")}
                  </p>
                </div>
              ) : (
                cartData?.items?.map((item) => {
                  // Compute the fallback logic for this specific item.
                  const shouldShowFallback = !item?.package?.image || imgError;

                  // Determine the icon component based on the category.
                  const IconComponent =
                    categoryIcons[item?.package?.brand?.category?.name] ||
                    ShoppingCart;
                  return (
                    <div
                      key={item._id}
                      className="flex items-center gap-4 py-2 px-2 border-b border-gray-200"
                    >
                      <IconWrapper>
                        {shouldShowFallback ? (
                          <ShoppingCart className="h-6 w-6 text-blue-600" />
                        ) : (
                          <IconComponent className="h-6 w-6 text-blue-600" />
                        )}
                      </IconWrapper>
                      <div className="flex flex-col flex-grow">
                        <p className="font-medium text-sm text-gray-800">
                          {`${item.package?.reference}`}
                        </p>
                        <p className="text-sm text-gray-600">
                          {`MAD ${item?.package?.price} x ${item.quantity}`}
                        </p>
                        {item?.package?.brand?.category?.name !==
                        "web creation" ? (
                          <p className="text-sm font-medium text-gray-800">
                            {`${t("period")} ${item.period} ${
                              item.period > 1 ? t("months") : t("month")
                            }`}
                          </p>
                        ) : null}

                        {item.discount > 0 && (
                          <p className="text-sm font-medium text-gray-800">
                            {t("discount")} :{" "}
                            <span className="text-green-800">
                              {" "}
                              -
                              {!isNaN(item.discount) && item.price
                                ? (
                                    (item.discount /
                                      (item.price *
                                        item.quantity *
                                        item.period)) *
                                    100
                                  ).toFixed(0)
                                : "0.00"}
                              %
                            </span>
                          </p>
                        )}
                      </div>

                      {/* Item Actions */}
                      <div className="flex flex-col items-center h-full justify-between gap-y-2">
                        <div className="flex items-center gap-2 border border-gray-50 rounded-full p-0 shadow-none">
                          <button
                          name="Decrease"
                            className="text-black text-lg w-6 h-6 flex items-center justify-center rounded-full bg-gray-50 hover:bg-gray-100 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                            onClick={() =>
                              handleQuantityChange(item?.package._id, -1, 1)
                            }
                            aria-label="Decrease quantity"
                          >
                            -
                          </button>
                          <span className="text-black font-medium text-sm px-2">
                            {item.quantity}
                          </span>
                          <button
                          name="Increase"
                            className="text-black text-lg w-6 h-6 flex items-center justify-center rounded-full bg-gray-50 hover:bg-gray-100 shadow-none"
                            onClick={() =>
                              handleQuantityChange(item?.package._id, 1, 1)
                            }
                            aria-label="Increase quantity"
                          >
                            +
                          </button>
                        </div>

                        <button
                        name="Delete"
                          className="text-sm text-red-500 flex flex-row gap-x-2 items-center justify-center hover:bg-red-500 hover:bg-opacity-80 hover:text-white p-1 rounded-full"
                          onClick={() =>
                            handleQuantityChange(
                              item?.package._id,
                              -1,
                              item?.quantity == 1 ? "delete" : item?.quantity
                            )
                          }
                        >
                          <TrashIcon width={16} className="my-auto" />
                          <span className="text-sm my-auto capitalize">
                            {t("delete")}
                          </span>
                        </button>
                      </div>
                    </div>
                  );
                })
              )}
            </div>

            {/* Footer */}
            {cartCount !== 0 && (
              <div className="relative flex justify-between text-sm mt-5">
                <span className="text-gray-600">{t("total")}</span>
                <span className="font-medium">
                  <span className="text-[12px]">MAD</span>{" "}
                  {(isNaN(cartData?.totalPrice)
                    ? 0
                    : cartData?.totalPrice
                  ).toFixed(2)}{" "}
                  <small className="font-medium">(HT)</small>
                </span>
                {cartData?.totalDiscount > 0 && (
                  <div className="absolute -right-[1px] -top-[18px] ">
                    <small className="font-medium pl-0 text-gray-800">
                      <del>
                        <span className="text-[12px]">MAD</span>{" "}
                        {(isNaN(cartData?.totalPrice + cartData?.totalDiscount)
                          ? 0
                          : cartData?.totalPrice + cartData?.totalDiscount
                        ).toFixed(2)}
                      </del>
                    </small>
                    <small className="ml-2 font-medium pl-0 text-green-800">
                      (-
                      {isNaN(
                        (cartData?.totalDiscount /
                          (cartData?.totalPrice + cartData?.totalDiscount)) *
                          100
                      )
                        ? 0
                        : (
                            (cartData?.totalDiscount /
                              (cartData?.totalPrice +
                                cartData?.totalDiscount)) *
                            100
                          ).toFixed(0)}
                      %)
                    </small>
                  </div>
                )}
              </div>
            )}
            <button
            name="Open Menu"
              className="mt-4 bg-blue-600 text-white hover:bg-blue-700 rounded-lg w-full py-2 text-sm font-medium font-poppins uppercase"
              onClick={() => {
                setIsCartMenuOpen(false);
                router.push("/client/cart");
              }}
            >
              {t("go_to_cart")}
            </button>
          </div>
        )}
      </div>

      {/* User Avatar and Dropdown Menu for Logged-in Users */}
      {user?.state !== AccountState.GUEST ? (
        <AvatarIcon />
      ) : (
        <Button
        id="Login"
          name="Login"
          aria-label="Login"
          variant="text"
          color="blue-gray"
          className="flex items-center justify-center rounded-full p-0 m-1 hover:border-secondary border-2 border-transparent flex-shrink-0 h-fit my-auto"
          onClick={() => navigate("login")}
        >
          <UserCircleIcon className="w-8 h-8" />
        </Button>
      )}
    </div>
  );
}
