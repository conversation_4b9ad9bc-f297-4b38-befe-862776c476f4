const Order = require("../models/Order");
const SubOrder = require("../models/SubOrder");
const Cart = require("../models/Cart");
const OrderStatus = require("../constants/enums/order-status");
const PaymentMethod = require("../constants/enums/payment-method");
const Category = require("../models/Category");
const Brand = require("../models/Brand");
const Package = require("../models/Package");
const ProductStatus = require("../constants/enums/poduct-status");
const { processPayment } = require("./paymentController");
const { v4: uuidv4 } = require("uuid");

// Create a new order from the cart
exports.createOrder = async (req, res) => {
  console.log("Create Order......");
  try {
    const userId = req.user?._id;
    const {
      BillToName,
      email,
      phone,
      address,
      country,
      isCompany,
      companyICE,
      companyEmail,
      companyPhone,
      companyAddress,
    } = req.body;

    // const cart = await Cart.findOne({ user: userId });
    const cart = await Cart.findOne({ user: userId }).populate("items.package");
    if (!cart || cart.items.length === 0) {
      return res.status(400).json({
        cartIsEmpty: true,
        message: req.t("order.cart_is_empty"),
      });
    }

    const shippingFee = 0;
    const tax = 0.2;
    let totalPrice = cart.totalPrice * (1 + tax);
    let taxAmount = cart.totalPrice * tax;
    let orderIdentifiant = uuidv4();

    let totalDiscount = 0;
    let subTotal = 0;
    // Create subOrders from cart items
    console.log(
      "Cart items:",
      cart.items.map((item) => ({
        type: item.type,
        package: item.package ? item.package._id : "N/A",
        domainName: item.domainName || "N/A",
        price: item.price,
        period: item.period,
      }))
    );

    const subOrders = await Promise.all(
      cart.items.map(async (item) => {
        totalDiscount += item.discount;
        const basedPrice = item.price * item.quantity * item.period;
        subTotal = subTotal + basedPrice;
        const subOrderIdentifiant = uuidv4().split("-")[0];

        // Handle domain items differently
        if (item.type === "domain") {
          console.log("Processing domain item:", item.domainName);
          // For domain items, we need to find or create a package for the domain
          const Package = require("../models/Package");
          const Brand = require("../models/Brand");
          const Category = require("../models/Category");
          const ProductStatus = require("../constants/enums/poduct-status");

          // Find or create the Domain category
          let domainCategory = await Category.findOne({ name: "Domains" });
          if (!domainCategory) {
            domainCategory = new Category({
              name: "Domains",
              name_fr: "Domaines",
              description: "Domain registration services",
              description_fr: "Services d'enregistrement de domaines",
            });
            await domainCategory.save();
          }

          // Find or create the Domain brand
          let domainBrand = await Brand.findOne({
            name: "Domain Registration",
          });
          if (!domainBrand) {
            domainBrand = new Brand({
              name: "Domain Registration",
              name_fr: "Enregistrement de Domaine",
              description: "Domain registration services",
              description_fr: "Services d'enregistrement de domaines",
              category: domainCategory._id,
            });
            await domainBrand.save();
          }

          // Create a reference for the domain package
          const domainRef = `domain-${item.domainName}`;
          console.log("Domain reference:", domainRef);

          // Find or create a package for this domain
          let domainPackage = await Package.findOne({ reference: domainRef });
          if (!domainPackage) {
            console.log("Creating new domain package for:", item.domainName);
            domainPackage = new Package({
              reference: domainRef,
              name: item.domainName,
              name_fr: item.domainName,
              description: `Domain registration for ${item.domainName}`,
              description_fr: `Enregistrement de domaine pour ${item.domainName}`,
              price: item.price / item.period, // Store the price per period
              category: domainCategory._id,
              brand: domainBrand._id,
              status: ProductStatus.PUBLISHED,
            });
            await domainPackage.save();
            console.log("Domain package created with ID:", domainPackage._id);
          } else {
            console.log(
              "Found existing domain package with ID:",
              domainPackage._id
            );
          }

          console.log(
            "Creating SubOrder for domain with package ID:",
            domainPackage._id
          );
          const subOrder = new SubOrder({
            identifiant: subOrderIdentifiant,
            package: domainPackage._id,
            quantity: item.quantity,
            basedPrice, // Price before discount
            price: basedPrice - item.discount, // Price per item
            discount: item.discount, // Discount for this sub-order
            period: item.period,
          });

          try {
            await subOrder.save();
            console.log(
              "Domain SubOrder created successfully with ID:",
              subOrder._id
            );
            return subOrder._id;
          } catch (error) {
            console.error("Error creating domain SubOrder:", error);
            throw error;
          }
        } else {
          // For regular package items
          console.log(
            "Processing regular package item:",
            item.package ? item.package._id : "undefined"
          );

          if (!item.package) {
            console.error("Package is undefined for non-domain item:", item);
            throw new Error("Package is required for non-domain items");
          }

          const subOrder = new SubOrder({
            identifiant: subOrderIdentifiant,
            package: item.package,
            quantity: item.quantity,
            basedPrice, // Price before discount
            price: basedPrice - item.discount, // Price per item
            discount: item.discount, // Discount for this sub-order
            period: item.period,
          });

          try {
            await subOrder.save();
            console.log(
              "Regular package SubOrder created successfully with ID:",
              subOrder._id
            );
            return subOrder._id;
          } catch (error) {
            console.error("Error creating regular package SubOrder:", error);
            throw error;
          }
        }
      })
    );
    // totalPrice -= orderDiscount; // Apply discount to totalPrice

    // Create the order
    const newOrder = new Order({
      user: userId,
      identifiant: orderIdentifiant.split("-")[0],
      subOrders,
      paymentMethod: PaymentMethod.PAYZONE,
      status: OrderStatus.PENDING,
      taxRate: tax,
      totalPrice,
      totalDiscount,
      taxAmount,
      subTotal,
      shippingFee,
      billingInfo: {
        BillToName,
        email,
        phone,
        address,
        country,
        isCompany: isCompany || false,
        companyICE,
        companyEmail,
        companyPhone,
        companyAddress,
      },
      isPaid: false,
    });

    // Save the order to the database
    const savedOrder = await newOrder.save();
    // Fetch the populated order
    const order = await Order.findById(savedOrder._id).populate("user");
    // Process initial payment record
    // COMMENTED OUT: Payment will now be processed after successful CMI callback

    // const services = cart.items.map(item => ({
    //   serviceId: item.package,
    //   name: item.package?.name || 'Unknown Service',  // Ensure a default name
    //   period: item.period,
    //   price: item.price * item.quantity * item.period,
    //   discount: item.discount,
    //   quantity: item.quantity
    // }));

    // await processPayment({
    //   userId,
    //   orderId: order._id,
    //   totalPrice: order.totalPrice,
    //   subTotal: order.subTotal,
    //   totalDiscount: order.totalDiscount,
    //   taxAmount: order.taxAmount,
    //   paymentMethod: order.paymentMethod,
    //   status: 'completed',
    //   billingInfo: {
    //     name: BillToName,
    //     email,
    //     phone,
    //     address,
    //     country
    //   },
    //   services
    // });

    // Clear the cart after the order is created
    // await cart.clearCart();

    res.status(201).json({
      message: req.t("order.order_creation_success"),
      order,
    });
  } catch (error) {
    console.error("Order creation error:", error);
    if (error.name === "ValidationError") {
      console.error("Validation error details:", error.errors);
      return res.status(400).json({
        message: "Validation error",
        details: error.message,
        errors: Object.keys(error.errors).reduce((acc, key) => {
          acc[key] = error.errors[key].message;
          return acc;
        }, {}),
      });
    }
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

exports.getMyOrders = async (req, res) => {
  try {
    const userId = req.user?._id;

    // Retrieve orders and populate the package field in subOrders
    const orders = await Order.find({ user: userId }).populate({
      path: "subOrders",
      populate: {
        path: "package",
        model: "Package",
      },
    });

    if (orders.length === 0) {
      return res.status(400).json({ message: req.t("order.no_orders") });
    }

    res.status(200).json({
      message: req.t("order.order_retrieved_successfully"),
      orders,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Get a specific order
exports.getOrder = async (req, res) => {
  try {
    const { orderId } = req.params;
    console.log("getting order: ", orderId);
    const order = await Order.findOne({
      _id: orderId,
      status: { $ne: OrderStatus.DELETED },
    })
      .populate("user")
      .populate({
        path: "subOrders",
        populate: {
          path: "package",
          model: "Package",
        },
      });

    if (!order) {
      return res.status(404).json({ message: req.t("order.order_not_found") });
    }
    res.status(200).json({
      order,
      message: req.t("order_retrieved_successfully"),
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

exports.getSubOrdersByUserIdAndCategory = async (req, res) => {
  try {
    const userId = req.user?._id;
    const { categoryName } = req.params;
    console.log("getting categoryName____: ", categoryName);

    // Find the category by name
    const category = await Category.findOne({ name: categoryName });
    if (!category) {
      return res.status(404).json({ message: "Category not found" });
    }

    // Retrieve orders by userId and populate subOrders and package
    const orders = await Order.find({
      user: userId,
      status: {
        $nin: [OrderStatus.DELETED, OrderStatus.CANCELLED, OrderStatus.PENDING],
      },
    })
      .populate({
        path: "subOrders",
        populate: {
          path: "package",
          model: "Package",
          populate: {
            path: "brand",
            model: "Brand",
            select: "category name name_fr",
            match: { category: category._id },
          },
        },
      })
      .sort({ createdAt: -1 })
      .lean(); // Use lean to improve performance

    // Filter subOrders by category
    const subOrders = orders.reduce((acc, order) => {
      const filteredSubOrders = order.subOrders.filter(
        (subOrder) => subOrder.package && subOrder.package.brand
      );
      return acc.concat(filteredSubOrders);
    }, []);

    if (subOrders.length === 0) {
      return res.status(400).json({ message: "No suborders found" });
    }

    res.status(200).json({
      message: "Suborders retrieved successfully",
      subOrders,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Get user's domain orders
exports.getUserDomainOrders = async (req, res) => {
  try {
    const userId = req.user?._id;

    // Find the Domains category
    const category = await Category.findOne({ name: "Domains" });
    if (!category) {
      return res.status(404).json({ message: "Domains category not found" });
    }

    // Retrieve orders by userId and populate subOrders and package
    const orders = await Order.find({
      user: userId,
      status: {
        $nin: [OrderStatus.DELETED, OrderStatus.CANCELLED],
      },
    })
      .populate({
        path: "subOrders",
        populate: {
          path: "package",
          model: "Package",
          populate: {
            path: "brand",
            model: "Brand",
            select: "category name name_fr",
            match: { category: category._id },
          },
        },
      })
      .sort({ createdAt: -1 })
      .lean(); // Use lean to improve performance

    // Filter subOrders by domain category and transform to domain format
    const domainOrders = [];

    orders.forEach((order) => {
      const filteredSubOrders = order.subOrders.filter(
        (subOrder) => subOrder.package && subOrder.package.brand
      );

      filteredSubOrders.forEach((subOrder) => {
        // Format domain data
        domainOrders.push({
          id: subOrder._id,
          name: subOrder.package.name, // Domain name is stored in the package name
          status:
            order.status === OrderStatus.COMPLETED
              ? "active"
              : order.status === OrderStatus.PENDING
              ? "pending"
              : order.status,
          registrationDate: order.createdAt,
          expiryDate: new Date(
            new Date(order.createdAt).setFullYear(
              new Date(order.createdAt).getFullYear() + subOrder.period
            )
          ),
          autoRenew: false, // Default to false (auto-renewal disabled)
          registrar: "ZTech Domains",
          period: subOrder.period,
          price: subOrder.price,
          orderId: order._id,
          orderStatus: order.status,
          nameservers: ["ns1.ztech.com", "ns2.ztech.com"], // Default nameservers
          privacyProtection: true, // Default to true
        });
      });
    });

    res.status(200).json({
      message: "Domain orders retrieved successfully",
      domains: domainOrders,
    });
  } catch (error) {
    console.error("Error retrieving domain orders:", error);
    res.status(500).json({ message: "Server error" });
  }
};

// Update the order status (e.g., for processing, completed, etc.)
exports.updateOrderStatus = async (req, res) => {
  const { status } = req.body;
  const { orderId } = req.params;
  try {
    const order = await Order.findById(orderId);
    if (!order) {
      return res.status(404).json({ message: req.t("order_not_found") });
    }

    // Validate the new status
    const validStatuses = [
      OrderStatus.PENDING,
      OrderStatus.COMPLETED,
      OrderStatus.CANCELLED,
      OrderStatus.PROCESSING,
      OrderStatus.DELETED,
      OrderStatus.FAILED,
    ];

    if (!validStatuses.includes(status)) {
      return res.status(400).json({ message: "Invalid status" });
    }

    // Update the order status
    order.status = status;
    const savedOrder = await order.save();

    res.status(200).json({
      order: savedOrder,
      message: "Order status updated successfully",
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Mark the order as paid
exports.markOrderAsPaid = async (req, res) => {
  try {
    const { orderId } = req.params;
    const order = await Order.findById(orderId);
    if (!order) {
      return res.status(404).json({ message: "Order not found" });
    }

    order.isPaid = true;
    order.datePaid = Date.now();

    // Update payment record
    await processPayment({
      userId: order.user,
      orderId: order._id,
      totalPrice: order.totalPrice,
      paymentMethod: order.paymentMethod,
      status: "completed",
      transactionId: order.transactionId,
      billingInfo: order.billingInfo,
    });

    await order.save();

    res.status(200).json({
      order,
      message: "Order marked as paid successfully",
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Refund the order
exports.refundOrder = async (req, res) => {
  try {
    const { orderId } = req.params;
    const order = await Order.findById(orderId);
    if (!order) {
      return res.status(404).json({ message: "Order not found" });
    }

    order.status = OrderStatus.PROCESSINGREFUND; // Set the status to refunding
    await order.save();

    res.status(200).json({
      order,
      message: "Order refund requested successfully",
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Add this function to the exports

// Get a specific suborder by ID
exports.getSubOrderById = async (req, res) => {
  try {
    const { subOrderId } = req.params;
    const userId = req.user?._id;

    const subOrder = await SubOrder.findById(subOrderId).populate({
      path: "package",
      populate: {
        path: "brand",
        model: "Brand",
      },
    });

    if (!subOrder) {
      return res.status(404).json({ message: req.t("order.order_not_found") });
    }

    // Find the parent order to verify ownership
    const parentOrder = await Order.findOne({
      subOrders: subOrderId,
      user: userId,
    });

    if (!parentOrder) {
      return res.status(403).json({ message: req.t("errors.not_authorized") });
    }

    // Add the parent order reference to the subOrder
    const result = subOrder.toObject();
    result.parentOrder = parentOrder;

    res.status(200).json(result);
  } catch (error) {
    console.error("Error fetching suborder:", error);
    res.status(500).json({ message: req.t("errors.server_error") });
  }
};
