import React from "react";
import "../styles/globals.css"
// import { GoogleTagManager } from '@next/third-parties/google'
import { AuthProvider } from "./context/AuthContext";
import { GoogleOAuthProvider } from "@react-oauth/google";
import Script from "next/script";

export default async function RootLayout({ children }) {

  return (
    <html lang="en">
      <head>
        <title>ZtechEngineering | Web & Mobile Development, Cloud Hosting in Morocco</title>
        <meta
          name="description"
          content="ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success."
        />
        <meta
          name="keywords"
          content="web development Morocco, mobile applications Morocco, cloud hosting Morocco, digital agency, ZtechEngineering, website creation, cloud solutions, custom development, web mobile, PWA, digital transformation, web agency Morocco"
        />
        {/* <link rel="canonical" href="https://ztechengineering.com/en" /> */}

        <meta property="og:title" content="ZtechEngineering | Web & Mobile Development, Cloud Hosting in Morocco" />
        <meta property="og:description" content="ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success." />
        <meta property="og:url" content="https://ztechengineering.com/" />
        <meta property="og:image" content="https://ztechengineering.com/images/home/<USER>" />
        <meta property="og:site_name" content="Ztechengineering" />
        <meta property="article:publisher" content="https://web.facebook.com/profile.php?id=61551999353576&amp;_rdc=1&amp;_rdr" />

        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "ZtechEngineering",
              "url": "https://ztechengineering.com/",
              "logo": "https://ztechengineering.com/images/home/<USER>",
              "sameAs": [
                "https://web.facebook.com/profile.php?id=61551999353576&_rdc=1&_rdr",
                "https://www.youtube.com/@ztechengineering/",
                "https://www.instagram.com/ztechengineering",
                "https://www.linkedin.com/company/ztechengineering"
              ]
            })
          }}
        />

        <meta name="robots" content="index, follow" />
        <link rel="icon" href="/favicon.png" type="image/x-icon" />
        {/* <GoogleTagManager gtmId="GTM-WBVG4FCK" /> */}
        <meta
          name="google-site-verification"
          content="RokYIbdh-kKoq7cMq7qJURkC43dc7JgI3ojch4CL0RQ"
        />
        <link
          rel="alternate"
          hrefLang="fr"
          href="https://ztechengineering.com/fr"
        />
        <link
          rel="alternate"
          hrefLang="en"
          href="https://ztechengineering.com/en"
        />
        <Script
          strategy="worker"
          src={`https://www.googletagmanager.com/gtm.js?id=GTM-WBVG4FCK`}
        />
        <meta property="og:type" content="website" />
        <meta property="og:locale" content="en_US" />
        <meta property="og:locale:alternate" content="fr_FR" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="ZtechEngineering | Web & Mobile Development, Cloud Hosting in Morocco" />
        <meta name="twitter:description" content="ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success." />
        <meta name="twitter:image" content="https://ztechengineering.com/images/home/<USER>" />
        <meta name="twitter:site" content="@ztechengineering" />
        <meta name="twitter:creator" content="@ztechengineering" />
        <meta http-equiv="Content-Language" content="en" />
        <meta name="language" content="English" />
      </head>
      <body>
        <noscript>
          <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-WBVG4FCK"
            height="0"
            width="0"
            style={{ display: "none", visibility: "hidden" }}
          ></iframe>
        </noscript>
        <GoogleOAuthProvider clientId={process.env.GOOGLE_OAUTH_API_KEY}>
          <AuthProvider>{children}</AuthProvider>
        </GoogleOAuthProvider>
      </body>
    </html>
  );
}
