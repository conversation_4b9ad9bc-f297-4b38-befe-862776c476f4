const adminLogger = require("../utils/adminLogger");

/**
 * Middleware to log admin activities
 * @param {string} action - The action being performed (CREATE, UPDATE, DELETE, VIEW, etc.)
 * @param {string} targetModel - The model being affected
 * @param {Function} getTargetId - Function to extract target ID from request
 * @param {Function} getDetails - Function to extract details from request
 */
exports.logAdminAction = (action, targetModel, getTargetId, getDetails) => {
  return async (req, res, next) => {
    // Store the original send function
    const originalSend = res.send;

    // Override the send function
    res.send = function (data) {
      // Only log if the request was successful (status 2xx) and not a VIEW action
      if (res.statusCode >= 200 && res.statusCode < 300 && action !== "VIEW") {
        try {
          // Check if user is authenticated and is an admin
          if (!req.user || !req.user._id) {
            console.warn("No authenticated user found for activity logging");
          } else {
            const adminId = req.user._id;
            let parsedData;
            let target = null;
            let details = null;

            // Safely parse the response data
            try {
              parsedData = typeof data === "string" ? JSON.parse(data) : data;
            } catch (parseError) {
              console.warn(
                "Could not parse response data for activity logging:",
                parseError.message
              );
              parsedData = { rawData: "unparseable" };
            }

            // Extract target and details
            if (getTargetId) {
              try {
                target = getTargetId(req, parsedData);
              } catch (targetError) {
                console.warn(
                  "Error extracting target for activity logging:",
                  targetError.message
                );
              }
            }

            if (getDetails) {
              try {
                details = getDetails(req, parsedData);
              } catch (detailsError) {
                console.warn(
                  "Error extracting details for activity logging:",
                  detailsError.message
                );
              }
            }

            // Log the activity asynchronously (don't wait for it)
            adminLogger
              .logActivity(
                adminId,
                action,
                targetModel,
                target,
                adminLogger.sanitizeForLogging(details)
              )
              .catch((error) =>
                console.error(
                  "Error in admin activity logging middleware:",
                  error
                )
              );
          }
        } catch (error) {
          console.error("Error in admin activity logging middleware:", error);
        }
      }

      // Call the original send function
      return originalSend.call(this, data);
    };

    next();
  };
};

/**
 * Convenience middleware for common CRUD operations
 */
// VIEW actions are now skipped by default
exports.logViewAction = () => {
  console.warn("logViewAction is deprecated as VIEW actions are now skipped");
  return (_req, _res, next) => next(); // No-op middleware that just passes through
};

exports.logCreateAction = (targetModel, getTargetId, getDetails) =>
  exports.logAdminAction("CREATE", targetModel, getTargetId, getDetails);

exports.logUpdateAction = (targetModel, getTargetId, getDetails) =>
  exports.logAdminAction("UPDATE", targetModel, getTargetId, getDetails);

exports.logDeleteAction = (targetModel, getTargetId, getDetails) =>
  exports.logAdminAction("DELETE", targetModel, getTargetId, getDetails);
