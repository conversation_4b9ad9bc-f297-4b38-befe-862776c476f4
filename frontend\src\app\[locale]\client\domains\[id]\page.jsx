"use client";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import {
  Typo<PERSON>,
  Card,
  CardBody,
  Button,
  Tabs,
  TabsHeader,
  TabsBody,
  Tab,
  TabPanel,
  Switch,
} from "@material-tailwind/react";
import {
  Globe,
  ArrowLeft,
  Server,
  Shield,
  Lock,
  Mail,
  ExternalLink,
  RefreshCw,
} from "lucide-react";
import domainMngService from "@/app/services/domainMngService";

export default function DomainDetailPage({ params }) {
  const { id } = params;
  const t = useTranslations("client");
  const dt = useTranslations("client.domainWrapper");
  const [domain, setDomain] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const router = useRouter();

  useEffect(() => {
    const getDomainDetails = async () => {
      try {
        // This would be replaced with actual API call when implemented
        // const res = await domainMngService.getDomainById(id);
        // setDomain(res.data.domain);

        // For now, using mock data
        setTimeout(() => {
          setDomain({
            id: id,
            name:
              id === "dom1"
                ? "example.com"
                : id === "dom2"
                ? "mywebsite.net"
                : "businesssite.org",
            status: id === "dom3" ? "pending" : "active",
            registrationDate: "2023-01-15",
            expiryDate: "2024-01-15",
            autoRenew: id === "dom2" ? false : true,
            registrar: "ZTech Domains",
            nameservers: ["ns1.ztech.com", "ns2.ztech.com"],
            privacyProtection: id === "dom2" ? false : true,
            contacts: {
              registrant: {
                name: "John Doe",
                email: "<EMAIL>",
                phone: "+1234567890",
                address: "123 Main St, City, Country",
              },
              admin: {
                name: "John Doe",
                email: "<EMAIL>",
                phone: "+1234567890",
                address: "123 Main St, City, Country",
              },
              technical: {
                name: "Jane Smith",
                email: "<EMAIL>",
                phone: "+1987654321",
                address: "456 Tech St, City, Country",
              },
            },
            dnsRecords: [
              {
                id: "rec1",
                type: "A",
                name: "@",
                content: "***********",
                ttl: 3600,
              },
              {
                id: "rec2",
                type: "CNAME",
                name: "www",
                content: "@",
                ttl: 3600,
              },
              {
                id: "rec3",
                type: "MX",
                name: "@",
                content: "mail.example.com",
                priority: 10,
                ttl: 3600,
              },
            ],
          });
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error("Error getting domain details", error);
        setLoading(false);
      }
    };
    getDomainDetails();
  }, [id]);

  const handleAutoRenewToggle = async (value) => {
    try {
      // This would be replaced with actual API call when implemented
      // await domainMngService.toggleAutoRenewal(id, value);
      setDomain({ ...domain, autoRenew: value });
    } catch (error) {
      console.error("Error toggling auto renewal", error);
    }
  };

  const handlePrivacyToggle = async (value) => {
    try {
      // This would be replaced with actual API call when implemented
      // await domainMngService.togglePrivacyProtection(id, value);
      setDomain({ ...domain, privacyProtection: value });
    } catch (error) {
      console.error("Error toggling privacy protection", error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Globe className="h-6 w-6 text-blue-600" />
          </div>
          <Typography variant="h6" className="text-gray-600">
            {t("loading")}...
          </Typography>
        </div>
      </div>
    );
  }

  if (!domain) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-8">
        <Typography variant="h4" className="text-gray-800 font-bold mb-2">
          {t("domain_not_found", { defaultValue: "Domain Not Found" })}
        </Typography>
        <Button
          className="mt-4 bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
          onClick={() => router.push("/client/domains")}
        >
          <ArrowLeft className="h-4 w-4" />
          {dt("back_to_domains")}
        </Button>
      </div>
    );
  }

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        <Button
          variant="text"
          className="mb-6 text-blue-600 flex items-center gap-2"
          onClick={() => router.push("/client/domains")}
        >
          <ArrowLeft className="h-4 w-4" />
          {dt("back_to_domains")}
        </Button>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div className="flex items-center">
            <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
              <Globe className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <Typography
                variant="h1"
                className="text-2xl font-bold text-gray-800"
              >
                {domain.name}
              </Typography>
              <div className="flex items-center mt-1">
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize mr-2 ${
                    domain.status === "active"
                      ? "bg-green-100 text-green-800"
                      : domain.status === "pending"
                      ? "bg-yellow-100 text-yellow-800"
                      : domain.status === "expired"
                      ? "bg-red-100 text-red-800"
                      : "bg-gray-100 text-gray-800"
                  }`}
                >
                  {dt(domain.status)}
                </span>
                <Typography className="text-sm text-gray-500">
                  {dt("registrar")}: {domain.registrar}
                </Typography>
              </div>
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outlined"
              className="border-blue-600 text-blue-600 hover:bg-blue-50 flex items-center gap-2"
              onClick={() => window.open(`http://${domain.name}`, "_blank")}
            >
              {t("visit_website", { defaultValue: "Visit Website" })}
              <ExternalLink className="h-4 w-4" />
            </Button>
            <Button
              className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
              onClick={() => router.push(`/client/domains/${id}/renew`)}
            >
              {dt("renew_domain")}
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} className="mb-8">
          <TabsHeader
            className="bg-gray-100 rounded-lg p-1"
            indicatorProps={{
              className: "bg-white shadow-md rounded-md",
            }}
          >
            <Tab
              value="overview"
              onClick={() => setActiveTab("overview")}
              className={activeTab === "overview" ? "text-blue-600" : ""}
            >
              {t("overview", { defaultValue: "Overview" })}
            </Tab>
            <Tab
              value="dns"
              onClick={() => setActiveTab("dns")}
              className={activeTab === "dns" ? "text-blue-600" : ""}
            >
              {dt("dns_settings")}
            </Tab>
            <Tab
              value="contacts"
              onClick={() => setActiveTab("contacts")}
              className={activeTab === "contacts" ? "text-blue-600" : ""}
            >
              {dt("domain_contacts")}
            </Tab>
          </TabsHeader>
          <TabsBody>
            <TabPanel value="overview" className="p-0 mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
                  <CardBody className="p-6">
                    <Typography className="text-lg font-medium text-gray-900 mb-4">
                      {dt("domain_details")}
                    </Typography>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("domain_name")}
                        </Typography>
                        <Typography className="font-medium">
                          {domain.name}
                        </Typography>
                      </div>
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("status")}
                        </Typography>
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${
                            domain.status === "active"
                              ? "bg-green-100 text-green-800"
                              : domain.status === "pending"
                              ? "bg-yellow-100 text-yellow-800"
                              : domain.status === "expired"
                              ? "bg-red-100 text-red-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {dt(domain.status)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("registration_date")}
                        </Typography>
                        <Typography className="font-medium">
                          {new Date(
                            domain.registrationDate
                          ).toLocaleDateString()}
                        </Typography>
                      </div>
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("expiry_date")}
                        </Typography>
                        <Typography className="font-medium">
                          {new Date(domain.expiryDate).toLocaleDateString()}
                        </Typography>
                      </div>
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("auto_renew")}
                        </Typography>
                        <Switch
                          checked={domain.autoRenew}
                          onChange={(e) =>
                            handleAutoRenewToggle(e.target.checked)
                          }
                          color="blue"
                        />
                      </div>
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("whois_privacy")}
                        </Typography>
                        <Switch
                          checked={domain.privacyProtection}
                          onChange={(e) =>
                            handlePrivacyToggle(e.target.checked)
                          }
                          color="blue"
                        />
                      </div>
                    </div>
                  </CardBody>
                </Card>

                <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
                  <CardBody className="p-6">
                    <Typography className="text-lg font-medium text-gray-900 mb-4">
                      {dt("nameservers")}
                    </Typography>
                    <div className="space-y-4">
                      {domain.nameservers.map((ns, index) => (
                        <div
                          key={index}
                          className="flex justify-between items-center"
                        >
                          <Typography className="text-sm text-gray-500">
                            NS {index + 1}
                          </Typography>
                          <Typography className="font-medium">{ns}</Typography>
                        </div>
                      ))}
                      <div className="mt-6">
                        <Button
                          variant="outlined"
                          className="w-full border-blue-600 text-blue-600 hover:bg-blue-50"
                          onClick={() =>
                            router.push(`/client/domains/${id}/nameservers`)
                          }
                        >
                          {dt("update_nameservers")}
                        </Button>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            </TabPanel>

            <TabPanel value="dns" className="p-0 mt-6">
              {/* DNS Records Tab Content */}
              <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
                <CardBody className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <Typography className="text-lg font-medium text-gray-900">
                      {dt("manage_dns_records")}
                    </Typography>
                    <Button
                      className="bg-blue-600 hover:bg-blue-700"
                      onClick={() =>
                        router.push(`/client/domains/${id}/dns/add`)
                      }
                    >
                      {t("add_record", { defaultValue: "Add Record" })}
                    </Button>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="bg-gray-50 border-b border-gray-200">
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                            {t("type", { defaultValue: "Type" })}
                          </th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                            {t("name", { defaultValue: "Name" })}
                          </th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                            {t("content", { defaultValue: "Content" })}
                          </th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                            {t("ttl", { defaultValue: "TTL" })}
                          </th>
                          <th className="px-4 py-3 text-right text-sm font-medium text-gray-500">
                            {t("actions", { defaultValue: "Actions" })}
                          </th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {domain.dnsRecords.map((record) => (
                          <tr key={record.id} className="hover:bg-gray-50">
                            <td className="px-4 py-3 text-sm font-medium text-gray-900">
                              {record.type}
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-500">
                              {record.name}
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-500">
                              {record.content}
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-500">
                              {record.ttl}
                            </td>
                            <td className="px-4 py-3 text-right">
                              <Button
                                size="sm"
                                variant="text"
                                className="text-blue-600"
                                onClick={() =>
                                  router.push(
                                    `/client/domains/${id}/dns/${record.id}`
                                  )
                                }
                              >
                                {t("edit", { defaultValue: "Edit" })}
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardBody>
              </Card>
            </TabPanel>

            <TabPanel value="contacts" className="p-0 mt-6">
              {/* Domain Contacts Tab Content */}
              <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
                <CardBody className="p-6">
                  <Typography className="text-lg font-medium text-gray-900 mb-6">
                    {dt("domain_contacts")}
                  </Typography>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Typography className="font-medium text-gray-900 mb-2">
                        {t("registrant", {
                          defaultValue: "Registrant Contact",
                        })}
                      </Typography>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <Typography className="font-medium">
                          {domain.contacts.registrant.name}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.registrant.email}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.registrant.phone}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.registrant.address}
                        </Typography>
                      </div>
                    </div>
                    <div>
                      <Typography className="font-medium text-gray-900 mb-2">
                        {t("admin", { defaultValue: "Administrative Contact" })}
                      </Typography>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <Typography className="font-medium">
                          {domain.contacts.admin.name}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.admin.email}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.admin.phone}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.admin.address}
                        </Typography>
                      </div>
                    </div>
                    <div>
                      <Typography className="font-medium text-gray-900 mb-2">
                        {t("technical", { defaultValue: "Technical Contact" })}
                      </Typography>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <Typography className="font-medium">
                          {domain.contacts.technical.name}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.technical.email}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.technical.phone}
                        </Typography>
                        <Typography className="text-sm text-gray-500">
                          {domain.contacts.technical.address}
                        </Typography>
                      </div>
                    </div>
                  </div>
                  <div className="mt-6">
                    <Button
                      className="w-full bg-blue-600 hover:bg-blue-700"
                      onClick={() =>
                        router.push(`/client/domains/${id}/contacts`)
                      }
                    >
                      {dt("update_contacts")}
                    </Button>
                  </div>
                </CardBody>
              </Card>
            </TabPanel>
          </TabsBody>
        </Tabs>
      </div>
    </div>
  );
}
