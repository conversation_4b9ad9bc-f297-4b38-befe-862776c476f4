'use client'

import React, { useState, useEffect, Suspense } from 'react';
import {
  DollarSign,
  Users2,
  ShoppingCart,
  BarChart2,
  Package,
  MessageSquare,
  Globe,
  ArrowUpRight,
  Ticket,
  ChartLine,
  ChevronDown,
  ChevronUp,
  User,
  Timer,
} from 'lucide-react';
import { <PERSON>, Typo<PERSON>, Button } from "@material-tailwind/react";
import { useAuth } from '../context/AuthContext';
import dynamic from 'next/dynamic';
import { adminService } from "../../services/adminService";
import { motion } from 'framer-motion';


// Dynamically import charts to avoid SSR issues
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });


const ChartSuspence = () => {
  return (
    <div className='w-full h-full flex items-center justify-center' >
      <div className=" w-48 h-48 bg-gray-100 flex items-center rounded-full justify-center">
        <span className='w-24 h-24 bg-white flex items-center justify-center rounded-full'>
          <ChartLine className='w-14 h-14 animate-pulse' />
        </span>
      </div>
    </div>
  )
}

export default function Dashboard() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { user } = useAuth();
  const [dashboardStats, setDashboardStats] = useState(null);
  const [revenueView, setRevenueView] = useState('monthly'); // 'monthly' | 'weekly' | 'yearly'
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState("");
  const [brandDistribution, setBrandDistribution] = useState([]);
  const [loadingDistribution, setLoadingDistribution] = useState(false);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);

  const [stats, setStats] = useState({});


  // Fetch categories on mount
  useEffect(() => {
    async function fetchCategories() {
      const res = await adminService.getCategories();
      console.log('categories ', res);
      if (res.data && res.status === 200) {
        setCategories(res.data || []);
        if ((res.data || []).length > 0) {
          setSelectedCategory((res.data)[0]._id);
        }
      }
    }
    fetchCategories();
  }, []);


  // Fetch brand distribution when category changes
  useEffect(() => {
    if (!selectedCategory) return;
    setLoadingDistribution(true);
    adminService
      .getPackageDistribution(selectedCategory)
      .then((res) => {
        if (res && res.data && Array.isArray(res.data.data)) {
          setBrandDistribution(res.data.data);
        } else {
          setBrandDistribution([]);
        }
      })
      .finally(() => setLoadingDistribution(false));
      console.log('brandDistribution: ', brandDistribution);
  }, [selectedCategory]);

  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        setLoading(true);
        const response = await adminService.getDashboardStats();
        console.log('response.data.data ..: ', response.data.data);
        setStats(response.data.data);
      } catch (error) {
        console.error("Error fetching dashboard stats:", error);
        setError("Failed to load dashboard data");
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardStats();
  }, []);

  // Chart categories for each view
  const revenueCategories = {
    monthly: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    weekly: ['6d ago', '5d ago', '4d ago', '3d ago', '2d ago', '1d ago', 'Today'],
    yearly: dashboardStats?.yearlyRevenue ? Object.keys(dashboardStats.yearlyRevenue).sort() : [],
  };
  // Chart data for each view
  const revenueData = {
    monthly: dashboardStats?.monthlyRevenue || Array(12).fill(0),
    weekly: dashboardStats?.weeklyRevenue || Array(7).fill(0),
    yearly: dashboardStats?.yearlyRevenue
      ? Object.keys(dashboardStats.yearlyRevenue)
        .sort()
        .map((year) => dashboardStats.yearlyRevenue[year])
      : [],
  };

  // Chart options for revenue (dynamic x-axis)
  const baseRevenueChartOptions = {
    chart: {
      type: 'area',
      toolbar: { show: false },
      sparkline: { enabled: false },
    },
    colors: ['#F59E0B'],
    dataLabels: { enabled: false },
    stroke: { curve: 'smooth', width: 2 },
    xaxis: {
      categories: [], // will be overridden
      labels: {
        style: {
          colors: '#64748B',
          fontSize: '12px',
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          colors: '#64748B',
          fontSize: '12px',
        },
        formatter: function (val) {
          return val.toFixed(2);
        },
      },
    },
    legend: {
      position: 'top',
      horizontalAlign: 'right',
      fontSize: '14px',
      markers: { radius: 12 },
    },
    grid: {
      borderColor: '#E2E8F0',
      strokeDashArray: 4,
    },
  };

  const revenueChartOptions = {
    ...baseRevenueChartOptions,
    xaxis: {
      ...baseRevenueChartOptions.xaxis,
      categories: revenueCategories[revenueView],
    },
  };



  useEffect(() => {
    async function fetchDashboardStats() {
      try {
        const res = await adminService.getDashboardStats();
        console.log('res.data.data: ', res.data.data);
        if (res.data.success) {
          setDashboardStats(res.data.data);
        }
      } catch (err) {
        console.error("Failed to fetch dashboard stats", err);
      }
    }
    fetchDashboardStats();
  }, []);

  const revenueChartSeries = [
    {
      name: 'Revenue',
      data: revenueData[revenueView],
    },
  ];

  // Chart options for package sales
  const packageSalesOptions = {
    chart: {
      type: 'bar',
      toolbar: {
        show: false,
      },
    },
    plotOptions: {
      bar: {
        borderRadius: 4,
        horizontal: false,
        columnWidth: '55%',
      },
    },
    colors: ['#10B981', '#3B82F6', '#8B5CF6', '#F59E0B', '#EF4444', '#6366F1', '#F472B6', '#FBBF24'],
    dataLabels: {
      enabled: false,
    },
    stroke: {
      show: true,
      width: 2,
      colors: ['transparent'],
    },
    xaxis: {
      categories: brandDistribution.filter(b => b && typeof b.brandName === 'string').map((b) => b.brandName),
      labels: {
        style: {
          colors: '#64748B',
          fontSize: '12px',
        },
      },
    },
    yaxis: {
      title: {
        text: 'Sales',
        style: {
          color: '#64748B',
        },
      },
      labels: {
        style: {
          colors: '#64748B',
          fontSize: '12px',
        },
      },
    },
    fill: {
      opacity: 1,
    },
    tooltip: {
      y: {
        formatter: function (val) {
          return val + " sales";
        }
      }
    },
  };

  const packageSalesSeries = [
    {
      name: 'Sales',
      data: brandDistribution.filter(b => b && typeof b.sales === 'number').map((b) => b.sales),
    }
  ];

  // // Chart options for package distribution
  // const packageDistributionOptions = {
  //   chart: {
  //     type: 'donut',
  //     sparkline: {
  //       enabled: false,
  //     },
  //   },
  //   colors: ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B'],
  //   labels: ['Basic Hosting', 'Standard Hosting', 'Premium Hosting', 'Enterprise Hosting'],
  //   legend: {
  //     position: 'bottom',
  //     fontSize: '14px',
  //     markers: {
  //       radius: 12,
  //     },
  //   },
  //   dataLabels: {
  //     enabled: false,
  //   },
  //   plotOptions: {
  //     pie: {
  //       donut: {
  //         size: '65%',
  //         labels: {
  //           show: true,
  //           total: {
  //             show: true,
  //             label: 'Total',
  //             formatter: function (w) {
  //               return w.globals.seriesTotals.reduce((a, b) => a + b, 0);
  //             },
  //           },
  //         },
  //       },
  //     },
  //   },
  // };

  // const packageDistributionSeries = [42, 38, 25, 18];

  const packageDistributionOptions = {
    chart: { type: "donut", sparkline: { enabled: false } },
    colors: ["#3B82F6", "#8B5CF6", "#10B981", "#F59E0B", "#EF4444", "#6366F1", "#F472B6", "#FBBF24"],
    labels: brandDistribution.map((b) => b.brandName),
    legend: {
      position: "bottom",
      fontSize: "14px",
      markers: { radius: 12 },
    },
    dataLabels: { enabled: false },
    plotOptions: {
      pie: {
        donut: {
          size: "65%",
          labels: {
            show: true,
            total: {
              show: true,
              label: "Total",
              formatter: function (w) {
                return w.globals.seriesTotals.reduce((a, b) => a + b, 0);
              },
            },
          },
        },
      },
    },
  };

  const packageDistributionSeries = brandDistribution.map((b) => b.packageCount);

  // Chart options for SSL certificates
  const sslCertificatesOptions = {
    chart: {
      type: 'line',
      toolbar: {
        show: false,
      },
      sparkline: {
        enabled: false,
      },
    },
    colors: ['#10B981', '#F59E0B', '#EF4444'],
    dataLabels: {
      enabled: false,
    },
    stroke: {
      curve: 'smooth',
      width: 2,
    },
    xaxis: {
      categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      labels: {
        style: {
          colors: '#64748B',
          fontSize: '12px',
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          colors: '#64748B',
          fontSize: '12px',
        },
      },
    },
    legend: {
      position: 'top',
      horizontalAlign: 'right',
      fontSize: '14px',
      markers: {
        radius: 12,
      },
    },
    grid: {
      borderColor: '#E2E8F0',
      strokeDashArray: 4,
    },
  };

  const sslCertificatesSeries = [
    {
      name: 'Active',
      data: [25, 29, 32, 35, 38, 42],
    },
    {
      name: 'Expiring Soon',
      data: [5, 4, 6, 3, 7, 5],
    },
    {
      name: 'Expired',
      data: [2, 1, 3, 2, 1, 2],
    },
  ];

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{error}</h2>
          <button
            onClick={() => window.location.reload()}
            className="text-blue-600 hover:text-blue-700"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <Card className="bg-gradient-to-r from-blue-600 to-indigo-600 p-6 rounded-xl shadow-lg text-white">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <Typography variant="h4" className="text-white mb-2">
              Welcome back, {user?.firstName || 'Admin'}!
            </Typography>
            <Typography className="text-blue-100 max-w-2xl">
              Here{"'"}s an overview of your hosting platform{"'"}s performance. Manage your packages, orders, and support tickets from this dashboard.
            </Typography>
          </div>
        </div>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6 rounded-xl shadow-md hover:shadow-lg transition-all border-l-4 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="small" className="text-gray-600 mb-1">Total Users</Typography>
              <Typography variant="h4" className="font-bold">{stats.users}</Typography>
              <div className="flex items-center mt-1 text-green-500 text-sm">
                <ArrowUpRight className="h-4 w-4 mr-1" />
                <span>12.5% increase</span>
              </div>
            </div>
            <div className="h-12 w-12 rounded-full bg-blue-500/10 flex items-center justify-center">
              <Users2 className="h-6 w-6 text-blue-500" />
            </div>
          </div>
        </Card>

        <Card className="p-6 rounded-xl shadow-md hover:shadow-lg transition-all border-l-4 border-purple-500">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="small" className="text-gray-600 mb-1">Packages</Typography>
              <Typography variant="h4" className="font-bold">{stats.packages}</Typography>
              <div className="flex items-center mt-1 text-green-500 text-sm">
                <ArrowUpRight className="h-4 w-4 mr-1" />
                <span>8.3% increase</span>
              </div>
            </div>
            <div className="h-12 w-12 rounded-full bg-purple-500/10 flex items-center justify-center">
              <Package className="h-6 w-6 text-purple-500" />
            </div>
          </div>
        </Card>

        <Card className="p-6 rounded-xl shadow-md hover:shadow-lg transition-all border-l-4 border-green-500">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="small" className="text-gray-600 mb-1">Total Orders</Typography>
              <Typography variant="h4" className="font-bold">{stats.orders}</Typography>
              <div className="flex items-center mt-1 text-green-500 text-sm">
                <ArrowUpRight className="h-4 w-4 mr-1" />
                <span>15.2% increase</span>
              </div>
            </div>
            <div className="h-12 w-12 rounded-full bg-green-500/10 flex items-center justify-center">
              <ShoppingCart className="h-6 w-6 text-green-500" />
            </div>
          </div>
        </Card>

        <Card className="p-6 rounded-xl shadow-md hover:shadow-lg transition-all border-l-4 border-amber-500">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="small" className="text-gray-600 mb-1">Revenue</Typography>
              <Typography variant="h4" className="font-bold">MAD {stats.revenue?.toFixed(2).toLocaleString()}</Typography>
              <div className="flex items-center mt-1 text-green-500 text-sm">
                <ArrowUpRight className="h-4 w-4 mr-1" />
                <span>18.7% increase</span>
              </div>
            </div>
            <div className="h-12 w-12 rounded-full bg-amber-500/10 flex items-center justify-center">
              <DollarSign className="h-6 w-6 text-amber-500" />
            </div>
          </div>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Revenue Chart */}
        <Card className="p-6 rounded-xl shadow-md col-span-1 md:col-span-2">
          <div className="flex justify-between items-center mb-4">
            <Typography variant="h6" className="font-semibold">Revenue Overview</Typography>
            <div>
              <motion.div className="flex items-center gap-4 relative" layout>
                {['weekly', 'monthly', 'yearly'].map((view) => (
                  <button
                    key={view}
                    onClick={() => setRevenueView(view)}
                    className={`relative px-3 py-1 font-medium transition-colors duration-200 ${revenueView === view
                      ? 'text-blue-600'
                      : 'text-gray-500 hover:text-blue-500'
                      } bg-transparent outline-none`}
                    style={{ background: 'none', border: 'none' }}
                  >
                    <span className="capitalize">{view}</span>
                    {revenueView === view && (
                      <motion.div
                        layoutId="underline"
                        className="absolute left-0 right-0 -bottom-1 h-0.5 rounded-md bg-blue-600"
                        transition={{ type: "spring", stiffness: 500, damping: 30, duration: 0.5 }}
                      />
                    )}
                  </button>
                ))}
              </motion.div>
            </div>
          </div>
          <div className="h-80">
            {typeof window !== 'undefined' && (
              <Chart
                options={revenueChartOptions}
                series={revenueChartSeries}
                type="area"
                height={300}
              />
            )}
          </div>
        </Card>

        {/* Package Distribution */}
        <Card className="p-6 rounded-xl shadow-md">
          <div className='flex items-center justify-between mb-4'>
            <Typography variant="h6" className="font-semibold">Package Distribution</Typography>
            <div className='flex items-center justify-center gap-2'>
              {categories.length === 0 ? (
                <ChartSuspence />
              ) : (
                <div className="relative">
                  <motion.button
                    className="flex items-center justify-between w-48 px-4 py-2 border border-gray-200 rounded-md shadow-sm bg-white text-gray-700 font-medium focus:outline-none"
                    onClick={() => setShowCategoryDropdown((prev) => !prev)}
                    whileTap={{ scale: 0.97 }}
                    type="button"
                  >
                    <span>
                      {categories.find((cat) => cat._id === selectedCategory)?.name || "Select Category"}
                    </span>
                    {showCategoryDropdown ? <ChevronUp className='w-5 h-5' /> : <ChevronDown className='w-5 h-5' /> }
                  </motion.button>
                  {showCategoryDropdown && (
                    <motion.ul
                      initial={{ opacity: 0, y: -8 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -8 }}
                      className="absolute z-10 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg"
                    >
                      {categories.map((cat) => (
                        <li key={cat._id}>
                          <button
                            className={`w-full text-left px-4 py-2 hover:bg-blue-50 transition ${
                              selectedCategory === cat._id ? "bg-blue-100 text-blue-700 font-semibold" : ""
                            }`}
                            onClick={() => {
                              setSelectedCategory(cat._id);
                              setShowCategoryDropdown(false);
                            }}
                            type="button"
                          >
                            {cat.name}
                          </button>
                        </li>
                      ))}
                    </motion.ul>
                  )}
                </div>
              )}
            </div>
          </div>
          <div>
            {loadingDistribution ? (
              <ChartSuspence />
            ) : (
              <Chart
                options={packageDistributionOptions}
                series={packageDistributionSeries}
                type="donut"
                height={300}
              />
            )}
          </div>
          <div className="grid grid-cols-2 gap-2 mt-4">
            <Button variant="outlined" size="sm" className="flex items-center justify-center" onClick={() => window.location.href = '/admin/packages'}>
              <Package className="h-4 w-4 mr-2" />
              Manage Packages
            </Button>
            <Button variant="outlined" size="sm" className="flex items-center justify-center">
              <BarChart2 className="h-4 w-4 mr-2" />
              View Details
            </Button>
          </div>
        </Card>
      </div>

      {/* Package Sales & SSL Certificates */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6 rounded-xl shadow-md">
          <Typography variant="h6" className="font-semibold mb-4">Hosting Package Sales</Typography>
          <div className="h-72">
            {typeof window !== 'undefined' && (
              <Chart
                options={packageSalesOptions}
                series={packageSalesSeries}
                type="bar"
                height="100%"
              />
            )}
          </div>
          <div className="flex justify-end mt-2">
            <Button variant="text" size="sm" className="text-blue-500" onClick={() => window.location.href = '/admin/orders'}>
              View All Orders
            </Button>
          </div>
        </Card>

        <Card className="p-6 rounded-xl shadow-md">
          <Typography variant="h6" className="font-semibold mb-4">SSL Certificates Status</Typography>
          <div className="h-72">
            {typeof window !== 'undefined' && (
              <Chart
                options={sslCertificatesOptions}
                series={sslCertificatesSeries}
                type="line"
                height="100%"
              />
            )}
          </div>
          <div className="flex justify-end mt-2">
            <Button variant="text" size="sm" className="text-blue-500" onClick={() => window.location.href = '/admin/ssl'}>
              Manage SSL Certificates
            </Button>
          </div>
        </Card>
      </div>

      {/* Recent Orders & Support Tickets */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6 rounded-xl shadow-md col-span-1 md:col-span-2">
          <div className="flex justify-between items-center mb-4">
            <Typography variant="h6" className="font-semibold">Recent Orders</Typography>
            <Button variant="text" size="sm" className="text-primary" onClick={() => window.location.href = '/admin/orders'}>View All</Button>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {stats.recentOrders?.map((order) => (
                  <tr key={order.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">#{order.identifiant}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 flex items-center gap-0.5"> <User className='w-4 h-4' /> {order.customer}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{order.amount.toFixed(2)} <span className='font-semibold' >MAD</span> </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        ${order.status === 'completed' ? 'bg-green-100 text-green-800' :
                          order.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                            'bg-yellow-100 text-yellow-800'}`}>
                        {order.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 flex items-center gap-0.5"> <Timer className='w-4 h-4' /> {order.date}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>

        <Card className="p-6 rounded-xl shadow-md">
          <div className="flex justify-between items-center mb-4">
            <Typography variant="h6" className="font-semibold">Support Tickets</Typography>
            <div className="bg-blue-500 text-white text-xs font-medium px-2.5 py-0.5 rounded-full">
              {stats.support} Open
            </div>
          </div>

          <div className="space-y-3">
            {stats.recentTickets?.map((ticket, index) => (
              <div key={index} className="p-3 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-center mb-1">
                  <Typography variant="small" className="font-medium">{ticket.subject}</Typography>
                  <div className={`text-xs px-2 py-0.5 rounded-full ${ticket.priority === 'High' ? 'bg-red-100 text-red-800' :
                    ticket.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                    {ticket.priority}
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <Typography variant="small" className="text-gray-500">Status: {ticket.status}</Typography>
                  <Button variant="text" size="sm" className="p-0 text-primary">View</Button>
                </div>
              </div>
            ))}

            <Button variant="outlined" fullWidth className="flex items-center justify-center mt-2" onClick={() => window.location.href = '/admin/support'}>
              <MessageSquare className="h-4 w-4 mr-2" />
              Manage Support
            </Button>
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="p-6 rounded-xl shadow-md">
        <Typography variant="h6" className="font-semibold mb-4">Quick Actions</Typography>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { icon: Package, label: 'New Package', color: 'bg-purple-500', path: '/admin/packages' },
            { icon: Users2, label: 'Manage Users', color: 'bg-blue-500', path: '/admin/users' },
            { icon: Ticket, label: 'Support Tickets', color: 'bg-green-500', path: '/admin/support' },
            { icon: Globe, label: 'View Website', color: 'bg-orange-500', path: '/' },
          ].map((action, index) => (
            <Button
              key={index}
              variant="outlined"
              className="flex items-center justify-center gap-2 p-4 h-auto"
              onClick={() => window.location.href = action.path}
            >
              <div className={`${action.color} text-white p-2 rounded-full`}>
                <action.icon className="h-4 w-4" />
              </div>
              <span>{action.label}</span>
            </Button>
          ))}
        </div>
      </Card>
    </div>
  );

}
