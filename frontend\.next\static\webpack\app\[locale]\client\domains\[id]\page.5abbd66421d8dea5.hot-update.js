"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/components/domains/DomainRegistrationStatus.jsx":
/*!*************************************************************!*\
  !*** ./src/components/domains/DomainRegistrationStatus.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\n * Component to display domain registration status and allow manual registration\n */ const DomainRegistrationStatus = (param)=>{\n    let { domain, onRegistrationComplete } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { t } = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)(\"client\");\n    const handleRegisterDomain = async ()=>{\n        if (!domain.orderId) {\n            setError(\"Order ID is missing\");\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].registerDomainsForOrder(domain.orderId);\n            if (response.data.success) {\n                setSuccess(\"Domain registration initiated successfully\");\n                if (onRegistrationComplete) {\n                    onRegistrationComplete(response.data);\n                }\n            } else {\n                setError(response.data.message || \"Failed to register domain\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error registering domain:\", error);\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"An error occurred while registering the domain\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Determine if registration is needed based on domain status\n    const registrationNeeded = domain.status === \"pending\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-4\",\n        children: [\n            domain.status === \"active\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"green\",\n                className: \"mb-4\",\n                children: t(\"domain_active_message\", \"Your domain is active and ready to use.\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, undefined) : domain.status === \"pending\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"yellow\",\n                className: \"mb-4\",\n                children: t(\"domain_pending_message\", \"Your domain registration is pending. Click the button below to complete registration.\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"gray\",\n                className: \"mb-4\",\n                children: t(\"domain_status_message\", \"Domain status: \".concat(domain.status))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"red\",\n                className: \"mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, undefined),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"green\",\n                className: \"mb-4\",\n                children: success\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, undefined),\n            registrationNeeded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                className: \"bg-blue-600 hover:bg-blue-700\",\n                onClick: handleRegisterDomain,\n                disabled: loading,\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-4 w-4 mr-2 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                            lineNumber: 95,\n                            columnNumber: 15\n                        }, undefined),\n                        t(\"registering_domain\", \"Registering Domain...\")\n                    ]\n                }, void 0, true) : t(\"complete_domain_registration\", \"Complete Domain Registration\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DomainRegistrationStatus.jsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainRegistrationStatus, \"VNLlb64ArGb6ZUjZgo+DcrVkX2k=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations\n    ];\n});\n_c = DomainRegistrationStatus;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainRegistrationStatus);\nvar _c;\n$RefreshReg$(_c, \"DomainRegistrationStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/DomainRegistrationStatus.jsx\n"));

/***/ })

});