"use client"

import React, { useState, useEffect } from 'react';
import { Save, Globe, Share2, Mail, Phone, MapPin, AlertCircle, CheckCircle, Search, Link as LinkIcon, Settings as SettingsIcon } from 'lucide-react';

// Dummy data for site settings
const dummySettings = {
  general: {
    siteName: 'International Responder Systems',
    siteDescription: 'Healthcare Emergency Response Solutions',
    contactEmail: '<EMAIL>',
    phone: '',
    address: '157 E Main Street, Elkton, MD 21921-5977',
    socialLinks: {
      linkedin: 'https://www.linkedin.com/company/international-responder-systems',
      twitter: 'https://twitter.com/intrespondersys',
      facebook: 'https://www.facebook.com/InternationalResponderSystems'
    }
  },
  seo: {
    defaultTitle: 'International Responder Systems - Healthcare Emergency Response Solutions',
    defaultDescription: 'Leading provider of healthcare emergency response and grant management solutions.',
    defaultKeywords: 'healthcare, emergency response, grant management, SOAR, GrantReady',
    googleAnalyticsId: '',
    googleSiteVerification: '',
    bingVerification: '',
    robotsTxt: 'User-agent: *\nAllow: /',
    sitemapEnabled: true
  },
  appearance: {
    logo: '',
    favicon: '',
    primaryColor: '#2563eb',
    secondaryColor: '#1e40af',
    fontFamily: 'Inter'
  },
  features: {
    blogEnabled: true,
    commentsEnabled: true,
    userRegistrationEnabled: true,
    maintenanceMode: false
  }
};

export default function SettingsManagement() {
  const [settings, setSettings] = useState(dummySettings);
  const [activeTab, setActiveTab] = useState('general');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      // Simulate fetching data
      setSettings(dummySettings);
    } catch (err) {
      console.error('Error fetching settings:', err);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Simulate saving settings
      setSuccess('Settings saved successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      setError('Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'general', label: 'General', icon: SettingsIcon },
    { id: 'seo', label: 'SEO', icon: Search },
    { id: 'appearance', label: 'Appearance', icon: Globe },
    { id: 'features', label: 'Features', icon: Share2 }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Site Name</label>
              <input
                type="text"
                value={settings.general.siteName}
                onChange={(e) => setSettings({
                  ...settings,
                  general: { ...settings.general, siteName: e.target.value }
                })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Site Description</label>
              <textarea
                value={settings.general.siteDescription}
                onChange={(e) => setSettings({
                  ...settings,
                  general: { ...settings.general, siteDescription: e.target.value }
                })}
                rows={3}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Contact Email</label>
              <input
                type="email"
                value={settings.general.contactEmail}
                onChange={(e) => setSettings({
                  ...settings,
                  general: { ...settings.general, contactEmail: e.target.value }
                })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Phone</label>
              <input
                type="tel"
                value={settings.general.phone}
                onChange={(e) => setSettings({
                  ...settings,
                  general: { ...settings.general, phone: e.target.value }
                })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Address</label>
              <textarea
                value={settings.general.address}
                onChange={(e) => setSettings({
                  ...settings,
                  general: { ...settings.general, address: e.target.value }
                })}
                rows={2}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Social Links</h3>
              <div>
                <label className="block text-sm font-medium text-gray-700">LinkedIn</label>
                <input
                  type="url"
                  value={settings.general.socialLinks.linkedin}
                  onChange={(e) => setSettings({
                    ...settings,
                    general: {
                      ...settings.general,
                      socialLinks: { ...settings.general.socialLinks, linkedin: e.target.value }
                    }
                  })}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Twitter</label>
                <input
                  type="url"
                  value={settings.general.socialLinks.twitter}
                  onChange={(e) => setSettings({
                    ...settings,
                    general: {
                      ...settings.general,
                      socialLinks: { ...settings.general.socialLinks, twitter: e.target.value }
                    }
                  })}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Facebook</label>
                <input
                  type="url"
                  value={settings.general.socialLinks.facebook}
                  onChange={(e) => setSettings({
                    ...settings,
                    general: {
                      ...settings.general,
                      socialLinks: { ...settings.general.socialLinks, facebook: e.target.value }
                    }
                  })}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        );

      case 'seo':
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Default Meta Title</label>
              <input
                type="text"
                value={settings.seo.defaultTitle}
                onChange={(e) => setSettings({
                  ...settings,
                  seo: { ...settings.seo, defaultTitle: e.target.value }
                })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
              <p className="mt-1 text-sm text-gray-500">
                Recommended length: 50-60 characters
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Default Meta Description</label>
              <textarea
                value={settings.seo.defaultDescription}
                onChange={(e) => setSettings({
                  ...settings,
                  seo: { ...settings.seo, defaultDescription: e.target.value }
                })}
                rows={3}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
              <p className="mt-1 text-sm text-gray-500">
                Recommended length: 150-160 characters
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Default Keywords</label>
              <input
                type="text"
                value={settings.seo.defaultKeywords}
                onChange={(e) => setSettings({
                  ...settings,
                  seo: { ...settings.seo, defaultKeywords: e.target.value }
                })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
              <p className="mt-1 text-sm text-gray-500">
                Comma-separated keywords
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Google Analytics ID</label>
              <input
                type="text"
                value={settings.seo.googleAnalyticsId}
                onChange={(e) => setSettings({
                  ...settings,
                  seo: { ...settings.seo, googleAnalyticsId: e.target.value }
                })}
                placeholder="G-XXXXXXXXXX"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Google Site Verification</label>
              <input
                type="text"
                value={settings.seo.googleSiteVerification}
                onChange={(e) => setSettings({
                  ...settings,
                  seo: { ...settings.seo, googleSiteVerification: e.target.value }
                })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Bing Verification</label>
              <input
                type="text"
                value={settings.seo.bingVerification}
                onChange={(e) => setSettings({
                  ...settings,
                  seo: { ...settings.seo, bingVerification: e.target.value }
                })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">robots.txt Content</label>
              <textarea
                value={settings.seo.robotsTxt}
                onChange={(e) => setSettings({
                  ...settings,
                  seo: { ...settings.seo, robotsTxt: e.target.value }
                })}
                rows={4}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 font-mono"
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                checked={settings.seo.sitemapEnabled}
                onChange={(e) => setSettings({
                  ...settings,
                  seo: { ...settings.seo, sitemapEnabled: e.target.checked }
                })}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-700">
                Enable XML Sitemap
              </label>
            </div>
          </div>
        );

      case 'appearance':
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Logo URL</label>
              <input
                type="url"
                value={settings.appearance.logo}
                onChange={(e) => setSettings({
                  ...settings,
                  appearance: { ...settings.appearance, logo: e.target.value }
                })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Favicon URL</label>
              <input
                type="url"
                value={settings.appearance.favicon}
                onChange={(e) => setSettings({
                  ...settings,
                  appearance: { ...settings.appearance, favicon: e.target.value }
                })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Primary Color</label>
              <div className="mt-1 flex items-center space-x-2">
                <input
                  type="color"
                  value={settings.appearance.primaryColor}
                  onChange={(e) => setSettings({
                    ...settings,
                    appearance: { ...settings.appearance, primaryColor: e.target.value }
                  })}
                  className="h-8 w-8 rounded-md border border-gray-300"
                />
                <input
                  type="text"
                  value={settings.appearance.primaryColor}
                  onChange={(e) => setSettings({
                    ...settings,
                    appearance: { ...settings.appearance, primaryColor: e.target.value }
                  })}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Secondary Color</label>
              <div className="mt-1 flex items-center space-x-2">
                <input
                  type="color"
                  value={settings.appearance.secondaryColor}
                  onChange={(e) => setSettings({
                    ...settings,
                    appearance: { ...settings.appearance, secondaryColor: e.target.value }
                  })}
                  className="h-8 w-8 rounded-md border border-gray-300"
                />
                <input
                  type="text"
                  value={settings.appearance.secondaryColor}
                  onChange={(e) => setSettings({
                    ...settings,
                    appearance: { ...settings.appearance, secondaryColor: e.target.value }
                  })}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Font Family</label>
              <select
                value={settings.appearance.fontFamily}
                onChange={(e) => setSettings({
                  ...settings,
                  appearance: { ...settings.appearance, fontFamily: e.target.value }
                })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value="Inter">Inter</option>
                <option value="Roboto">Roboto</option>
                <option value="Open Sans">Open Sans</option>
                <option value="Lato">Lato</option>
              </select>
            </div>
          </div>
        );

      case 'features':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Blog</h3>
                <p className="text-sm text-gray-500">Enable or disable the blog functionality</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.features.blogEnabled}
                  onChange={(e) => setSettings({
                    ...settings,
                    features: { ...settings.features, blogEnabled: e.target.checked }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Comments</h3>
                <p className="text-sm text-gray-500">Allow users to comment on blog posts</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.features.commentsEnabled}
                  onChange={(e) => setSettings({
                    ...settings,
                    features: { ...settings.features, commentsEnabled: e.target.checked }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">User Registration</h3>
                <p className="text-sm text-gray-500">Allow new users to register</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.features.userRegistrationEnabled}
                  onChange={(e) => setSettings({
                    ...settings,
                    features: { ...settings.features, userRegistrationEnabled: e.target.checked }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Maintenance Mode</h3>
                <p className="text-sm text-gray-500">Enable maintenance mode to show a maintenance page</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.features.maintenanceMode}
                  onChange={(e) => setSettings({
                    ...settings,
                    features: { ...settings.features, maintenanceMode: e.target.checked }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-2xl font-bold">Site Settings</h2>
        <p className="text-gray-600">Manage your website&apos;s configuration and appearance</p>
      </div>

      {/* Alerts */}
      {error && (
        <div className="bg-red-50 text-red-600 p-4 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 text-green-600 p-4 rounded-lg flex items-center">
          <CheckCircle className="h-5 w-5 mr-2" />
          {success}
        </div>
      )}

      {/* Main Content */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 inline-flex items-center border-b-2 font-medium text-sm ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
              >
                <tab.icon className="h-5 w-5 mr-2" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="p-6">
            {renderTabContent()}
          </div>

          <div className="bg-gray-50 px-6 py-4 flex justify-end">
            <button
              type="submit"
              disabled={loading}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50 flex items-center"
            >
              <Save className="h-5 w-5 mr-2" />
              {loading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}