{"name": "ztechengineering", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "deploy": "pm2 start npm --name ztech-frontend -- start -- --port=3001", "lint": "next lint", "lint:dir": "eslint ./src --ext .js,.jsx", "lint:quiet": "eslint ./src --ext .js,.jsx --quiet"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.9.1", "@material-tailwind/react": "^2.1.10", "@next/third-parties": "^15.0.3", "@notionhq/client": "^2.2.15", "@react-oauth/google": "^0.12.1", "@react-pdf/renderer": "^4.2.2", "axios": "^1.7.9", "countries-list": "^3.1.1", "framer-motion": "^11.18.2", "joi": "^17.13.3", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lottie-react": "^2.4.1", "lucide-react": "^0.475.0", "next": "14.1.0", "next-intl": "^3.5.3", "nodemailer": "^6.9.16", "react": "^18", "react-apexcharts": "^1.7.0", "react-cookie": "^7.2.2", "react-dom": "^18", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.53.2", "react-icons": "^5.3.0", "react-intersection-observer": "^9.16.0", "react-loading-skeleton": "^3.5.0", "react-markdown": "^9.0.3", "react-slick": "^0.30.2", "react-toastify": "^10.0.6", "sharp": "^0.33.5", "slick-carousel": "^1.8.1", "swiper": "^11.2.5", "xlsx": "^0.18.5", "yup": "^1.4.0", "ztechengineering": "file:"}, "devDependencies": {"@builder.io/partytown": "^0.10.3", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14"}, "packageManager": "pnpm@10.5.2+sha512.da9dc28cd3ff40d0592188235ab25d3202add8a207afbedc682220e4a0029ffbff4562102b9e6e46b4e3f9e8bd53e6d05de48544b0c57d4b0179e22c76d1199b"}