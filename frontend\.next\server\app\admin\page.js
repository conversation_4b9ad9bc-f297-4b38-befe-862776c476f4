/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/page";
exports.ids = ["app/admin/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.jsx&appDir=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.jsx&appDir=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport safe */ C_Users_abder_Desktop_Work_ztech_new_env_ztech_dev_frontend_src_app_global_error_jsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var C_Users_abder_Desktop_Work_ztech_new_env_ztech_dev_frontend_src_app_global_error_jsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./src/app/global-error.jsx */ \"(rsc)/./src/app/global-error.jsx\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/page.jsx */ \"(rsc)/./src/app/admin/page.jsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/layout.jsx */ \"(rsc)/./src/app/admin/layout.jsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.jsx */ \"(rsc)/./src/app/layout.jsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.jsx */ \"(rsc)/./src/app/loading.jsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\loading.jsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.js */ \"(rsc)/./src/app/not-found.js\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\not-found.js\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/admin/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/page\",\n        pathname: \"/admin\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.jsx&appDir=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5C%40react-oauth%5Cgoogle%5Cdist%5Cindex.esm.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5Ccontext%5CAuthContext.jsx&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Cstyles%5Cglobals.css&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5C%40react-oauth%5Cgoogle%5Cdist%5Cindex.esm.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5Ccontext%5CAuthContext.jsx&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Cstyles%5Cglobals.css&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@react-oauth/google/dist/index.esm.js */ \"(ssr)/./node_modules/@react-oauth/google/dist/index.esm.js\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/context/AuthContext.jsx */ \"(ssr)/./src/app/context/AuthContext.jsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDYWJkZXIlNUNEZXNrdG9wJTVDV29yayU1Q3p0ZWNoX25ld19lbnYlNUN6dGVjaF9kZXYlNUNmcm9udGVuZCU1Q25vZGVfbW9kdWxlcyU1QyU0MHJlYWN0LW9hdXRoJTVDZ29vZ2xlJTVDZGlzdCU1Q2luZGV4LmVzbS5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q2FiZGVyJTVDRGVza3RvcCU1Q1dvcmslNUN6dGVjaF9uZXdfZW52JTVDenRlY2hfZGV2JTVDZnJvbnRlbmQlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q3NjcmlwdC5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q2FiZGVyJTVDRGVza3RvcCU1Q1dvcmslNUN6dGVjaF9uZXdfZW52JTVDenRlY2hfZGV2JTVDZnJvbnRlbmQlNUNzcmMlNUNhcHAlNUNjb250ZXh0JTVDQXV0aENvbnRleHQuanN4Jm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDYWJkZXIlNUNEZXNrdG9wJTVDV29yayU1Q3p0ZWNoX25ld19lbnYlNUN6dGVjaF9kZXYlNUNmcm9udGVuZCU1Q3NyYyU1Q3N0eWxlcyU1Q2dsb2JhbHMuY3NzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvTkFBZ0s7QUFDaEssb01BQXFKO0FBQ3JKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8/NWFmNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGFiZGVyXFxcXERlc2t0b3BcXFxcV29ya1xcXFx6dGVjaF9uZXdfZW52XFxcXHp0ZWNoX2RldlxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcQHJlYWN0LW9hdXRoXFxcXGdvb2dsZVxcXFxkaXN0XFxcXGluZGV4LmVzbS5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcYWJkZXJcXFxcRGVza3RvcFxcXFxXb3JrXFxcXHp0ZWNoX25ld19lbnZcXFxcenRlY2hfZGV2XFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXHNjcmlwdC5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcYWJkZXJcXFxcRGVza3RvcFxcXFxXb3JrXFxcXHp0ZWNoX25ld19lbnZcXFxcenRlY2hfZGV2XFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcY29udGV4dFxcXFxBdXRoQ29udGV4dC5qc3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5C%40react-oauth%5Cgoogle%5Cdist%5Cindex.esm.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5Ccontext%5CAuthContext.jsx&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Cstyles%5Cglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5Cadmin%5Clayout.jsx&server=true!":
/*!****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5Cadmin%5Clayout.jsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/layout.jsx */ \"(ssr)/./src/app/admin/layout.jsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDYWJkZXIlNUNEZXNrdG9wJTVDV29yayU1Q3p0ZWNoX25ld19lbnYlNUN6dGVjaF9kZXYlNUNmcm9udGVuZCU1Q3NyYyU1Q2FwcCU1Q2FkbWluJTVDbGF5b3V0LmpzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLz85ZTY5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcYWJkZXJcXFxcRGVza3RvcFxcXFxXb3JrXFxcXHp0ZWNoX25ld19lbnZcXFxcenRlY2hfZGV2XFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcYWRtaW5cXFxcbGF5b3V0LmpzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5Cadmin%5Clayout.jsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5Cadmin%5Cpage.jsx&server=true!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5Cadmin%5Cpage.jsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/page.jsx */ \"(ssr)/./src/app/admin/page.jsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDYWJkZXIlNUNEZXNrdG9wJTVDV29yayU1Q3p0ZWNoX25ld19lbnYlNUN6dGVjaF9kZXYlNUNmcm9udGVuZCU1Q3NyYyU1Q2FwcCU1Q2FkbWluJTVDcGFnZS5qc3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8/MzJhMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGFiZGVyXFxcXERlc2t0b3BcXFxcV29ya1xcXFx6dGVjaF9uZXdfZW52XFxcXHp0ZWNoX2RldlxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGFkbWluXFxcXHBhZ2UuanN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5Cadmin%5Cpage.jsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5Cglobal-error.jsx&server=true!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5Cglobal-error.jsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/global-error.jsx */ \"(ssr)/./src/app/global-error.jsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDYWJkZXIlNUNEZXNrdG9wJTVDV29yayU1Q3p0ZWNoX25ld19lbnYlNUN6dGVjaF9kZXYlNUNmcm9udGVuZCU1Q3NyYyU1Q2FwcCU1Q2dsb2JhbC1lcnJvci5qc3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8/NTA5OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGFiZGVyXFxcXERlc2t0b3BcXFxcV29ya1xcXFx6dGVjaF9uZXdfZW52XFxcXHp0ZWNoX2RldlxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGdsb2JhbC1lcnJvci5qc3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5Cglobal-error.jsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/admin/layout.jsx":
/*!**********************************!*\
  !*** ./src/app/admin/layout.jsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../styles/globals.css */ \"(ssr)/./src/styles/globals.css\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context/AuthContext */ \"(ssr)/./src/app/context/AuthContext.jsx\");\n/* harmony import */ var _hook_useIsMobile__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../hook/useIsMobile */ \"(ssr)/./src/app/hook/useIsMobile.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _components_home_Notifications__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _components_avatar_UserAvatar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/avatar/UserAvatar */ \"(ssr)/./src/components/avatar/UserAvatar.jsx\");\n/* harmony import */ var _barrel_optimize_names_Bars3CenterLeftIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3CenterLeftIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/Bars3CenterLeftIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n// Dynamically import components that might cause hydration issues\nconst DynamicSidebar = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx -> \" + \"./sidebar\"\n        ]\n    },\n    ssr: false\n});\nfunction AdminLayout({ children }) {\n    // Use state to track if component is mounted\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [mobileSidebarOpen, setMobileSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    // Get mobile status and user info\n    const isMobile = (0,_hook_useIsMobile__WEBPACK_IMPORTED_MODULE_3__.useIsMobile)();\n    const { user } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    // Handle component mounting\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        setIsMounted(true);\n    }, []);\n    // Close mobile sidebar when screen size changes\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (!isMobile && isMounted) {\n            setMobileSidebarOpen(false);\n        }\n    }, [\n        isMobile,\n        isMounted\n    ]);\n    // Prevent hydration errors by rendering a simplified layout until client-side\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full flex flex-col min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed top-0 left-0 right-0 w-full h-16 bg-white border-b border-gray-200 z-30\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 pt-16 relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 md:p-6 min-h-[calc(100vh-4rem)]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-gray-200 rounded w-1/4 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-64 bg-gray-200 rounded mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-32 bg-gray-200 rounded mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                                lineNumber: 49,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full flex flex-col min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"fixed top-0 left-0 right-0 w-full h-16 bg-white border-b border-gray-200 z-30 flex items-center justify-between px-4 md:px-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setMobileSidebarOpen(!mobileSidebarOpen),\n                                className: \"p-2 rounded-md hover:bg-gray-100\",\n                                \"aria-label\": \"Toggle mobile menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3CenterLeftIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-7 w-7 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this),\n                            !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSidebarCollapsed(!sidebarCollapsed),\n                                className: \"p-2 rounded-md hover:bg-gray-100\",\n                                \"aria-label\": \"Toggle sidebar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3CenterLeftIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-7 w-7 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-inter text-gray-800\",\n                                children: \"Admin Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_Notifications__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this),\n                            user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_avatar_UserAvatar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                user: user,\n                                showName: !isMobile\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                                lineNumber: 95,\n                                columnNumber: 20\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 pt-16 relative\",\n                children: [\n                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: `fixed top-16 left-0 h-[calc(100vh-4rem)] bg-white border-r border-gray-200 z-20 transition-all duration-300 ${sidebarCollapsed ? \"w-16\" : \"w-64\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DynamicSidebar, {\n                            collapsed: sidebarCollapsed\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this),\n                    isMobile && mobileSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 z-40\",\n                        onClick: ()=>setMobileSidebarOpen(false),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                            className: \"fixed top-0 left-0 h-full w-64 bg-white border-r border-gray-200 z-50 transform transition-transform duration-300\",\n                            onClick: (e)=>e.stopPropagation(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-gray-800\",\n                                            children: \"Admin Menu\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setMobileSidebarOpen(false),\n                                            className: \"p-2 rounded-md hover:bg-gray-100\",\n                                            \"aria-label\": \"Close menu\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DynamicSidebar, {\n                                    collapsed: false\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: `flex-1 transition-all duration-300 ${!isMobile ? sidebarCollapsed ? \"ml-16\" : \"ml-64\" : \"ml-0\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 md:p-6 min-h-[calc(100vh-4rem)]\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin/layout.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/admin/page.jsx":
/*!********************************!*\
  !*** ./src/app/admin/page.jsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart2,ChartLine,ChevronDown,ChevronUp,DollarSign,Globe,MessageSquare,Package,ShoppingCart,Ticket,Timer,User,Users2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-line.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart2,ChartLine,ChevronDown,ChevronUp,DollarSign,Globe,MessageSquare,Package,ShoppingCart,Ticket,Timer,User,Users2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart2,ChartLine,ChevronDown,ChevronUp,DollarSign,Globe,MessageSquare,Package,ShoppingCart,Ticket,Timer,User,Users2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users-round.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart2,ChartLine,ChevronDown,ChevronUp,DollarSign,Globe,MessageSquare,Package,ShoppingCart,Ticket,Timer,User,Users2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart2,ChartLine,ChevronDown,ChevronUp,DollarSign,Globe,MessageSquare,Package,ShoppingCart,Ticket,Timer,User,Users2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart2,ChartLine,ChevronDown,ChevronUp,DollarSign,Globe,MessageSquare,Package,ShoppingCart,Ticket,Timer,User,Users2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart2,ChartLine,ChevronDown,ChevronUp,DollarSign,Globe,MessageSquare,Package,ShoppingCart,Ticket,Timer,User,Users2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart2,ChartLine,ChevronDown,ChevronUp,DollarSign,Globe,MessageSquare,Package,ShoppingCart,Ticket,Timer,User,Users2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart2,ChartLine,ChevronDown,ChevronUp,DollarSign,Globe,MessageSquare,Package,ShoppingCart,Ticket,Timer,User,Users2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart2,ChartLine,ChevronDown,ChevronUp,DollarSign,Globe,MessageSquare,Package,ShoppingCart,Ticket,Timer,User,Users2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart2,ChartLine,ChevronDown,ChevronUp,DollarSign,Globe,MessageSquare,Package,ShoppingCart,Ticket,Timer,User,Users2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/timer.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart2,ChartLine,ChevronDown,ChevronUp,DollarSign,Globe,MessageSquare,Package,ShoppingCart,Ticket,Timer,User,Users2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart2,ChartLine,ChevronDown,ChevronUp,DollarSign,Globe,MessageSquare,Package,ShoppingCart,Ticket,Timer,User,Users2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ticket.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart2,ChartLine,ChevronDown,ChevronUp,DollarSign,Globe,MessageSquare,Package,ShoppingCart,Ticket,Timer,User,Users2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(ssr)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/AuthContext */ \"(ssr)/./src/app/context/AuthContext.jsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _services_adminService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../services/adminService */ \"(ssr)/./src/services/adminService.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// Dynamically import charts to avoid SSR issues\nconst Chart = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx -> \" + \"react-apexcharts\"\n        ]\n    },\n    ssr: false\n});\nconst ChartSuspence = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \" w-48 h-48 bg-gray-100 flex items-center rounded-full justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"w-24 h-24 bg-white flex items-center justify-center rounded-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-14 h-14 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\nfunction Dashboard() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { user } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [dashboardStats, setDashboardStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [revenueView, setRevenueView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"monthly\"); // 'monthly' | 'weekly' | 'yearly'\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [brandDistribution, setBrandDistribution] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingDistribution, setLoadingDistribution] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCategoryDropdown, setShowCategoryDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch categories on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        async function fetchCategories() {\n            const res = await _services_adminService__WEBPACK_IMPORTED_MODULE_5__.adminService.getCategories();\n            console.log(\"categories \", res);\n            if (res.data && res.status === 200) {\n                setCategories(res.data || []);\n                if ((res.data || []).length > 0) {\n                    setSelectedCategory(res.data[0]._id);\n                }\n            }\n        }\n        fetchCategories();\n    }, []);\n    // Fetch brand distribution when category changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!selectedCategory) return;\n        setLoadingDistribution(true);\n        _services_adminService__WEBPACK_IMPORTED_MODULE_5__.adminService.getPackageDistribution(selectedCategory).then((res)=>{\n            if (res && res.data && Array.isArray(res.data.data)) {\n                setBrandDistribution(res.data.data);\n            } else {\n                setBrandDistribution([]);\n            }\n        }).finally(()=>setLoadingDistribution(false));\n        console.log(\"brandDistribution: \", brandDistribution);\n    }, [\n        selectedCategory\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchDashboardStats = async ()=>{\n            try {\n                setLoading(true);\n                const response = await _services_adminService__WEBPACK_IMPORTED_MODULE_5__.adminService.getDashboardStats();\n                console.log(\"response.data.data ..: \", response.data.data);\n                setStats(response.data.data);\n            } catch (error) {\n                console.error(\"Error fetching dashboard stats:\", error);\n                setError(\"Failed to load dashboard data\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchDashboardStats();\n    }, []);\n    // Chart categories for each view\n    const revenueCategories = {\n        monthly: [\n            \"Jan\",\n            \"Feb\",\n            \"Mar\",\n            \"Apr\",\n            \"May\",\n            \"Jun\",\n            \"Jul\",\n            \"Aug\",\n            \"Sep\",\n            \"Oct\",\n            \"Nov\",\n            \"Dec\"\n        ],\n        weekly: [\n            \"6d ago\",\n            \"5d ago\",\n            \"4d ago\",\n            \"3d ago\",\n            \"2d ago\",\n            \"1d ago\",\n            \"Today\"\n        ],\n        yearly: dashboardStats?.yearlyRevenue ? Object.keys(dashboardStats.yearlyRevenue).sort() : []\n    };\n    // Chart data for each view\n    const revenueData = {\n        monthly: dashboardStats?.monthlyRevenue || Array(12).fill(0),\n        weekly: dashboardStats?.weeklyRevenue || Array(7).fill(0),\n        yearly: dashboardStats?.yearlyRevenue ? Object.keys(dashboardStats.yearlyRevenue).sort().map((year)=>dashboardStats.yearlyRevenue[year]) : []\n    };\n    // Chart options for revenue (dynamic x-axis)\n    const baseRevenueChartOptions = {\n        chart: {\n            type: \"area\",\n            toolbar: {\n                show: false\n            },\n            sparkline: {\n                enabled: false\n            }\n        },\n        colors: [\n            \"#F59E0B\"\n        ],\n        dataLabels: {\n            enabled: false\n        },\n        stroke: {\n            curve: \"smooth\",\n            width: 2\n        },\n        xaxis: {\n            categories: [],\n            labels: {\n                style: {\n                    colors: \"#64748B\",\n                    fontSize: \"12px\"\n                }\n            }\n        },\n        yaxis: {\n            labels: {\n                style: {\n                    colors: \"#64748B\",\n                    fontSize: \"12px\"\n                },\n                formatter: function(val) {\n                    return val.toFixed(2);\n                }\n            }\n        },\n        legend: {\n            position: \"top\",\n            horizontalAlign: \"right\",\n            fontSize: \"14px\",\n            markers: {\n                radius: 12\n            }\n        },\n        grid: {\n            borderColor: \"#E2E8F0\",\n            strokeDashArray: 4\n        }\n    };\n    const revenueChartOptions = {\n        ...baseRevenueChartOptions,\n        xaxis: {\n            ...baseRevenueChartOptions.xaxis,\n            categories: revenueCategories[revenueView]\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        async function fetchDashboardStats() {\n            try {\n                const res = await _services_adminService__WEBPACK_IMPORTED_MODULE_5__.adminService.getDashboardStats();\n                console.log(\"res.data.data: \", res.data.data);\n                if (res.data.success) {\n                    setDashboardStats(res.data.data);\n                }\n            } catch (err) {\n                console.error(\"Failed to fetch dashboard stats\", err);\n            }\n        }\n        fetchDashboardStats();\n    }, []);\n    const revenueChartSeries = [\n        {\n            name: \"Revenue\",\n            data: revenueData[revenueView]\n        }\n    ];\n    // Chart options for package sales\n    const packageSalesOptions = {\n        chart: {\n            type: \"bar\",\n            toolbar: {\n                show: false\n            }\n        },\n        plotOptions: {\n            bar: {\n                borderRadius: 4,\n                horizontal: false,\n                columnWidth: \"55%\"\n            }\n        },\n        colors: [\n            \"#10B981\",\n            \"#3B82F6\",\n            \"#8B5CF6\",\n            \"#F59E0B\",\n            \"#EF4444\",\n            \"#6366F1\",\n            \"#F472B6\",\n            \"#FBBF24\"\n        ],\n        dataLabels: {\n            enabled: false\n        },\n        stroke: {\n            show: true,\n            width: 2,\n            colors: [\n                \"transparent\"\n            ]\n        },\n        xaxis: {\n            categories: brandDistribution.filter((b)=>b && typeof b.brandName === \"string\").map((b)=>b.brandName),\n            labels: {\n                style: {\n                    colors: \"#64748B\",\n                    fontSize: \"12px\"\n                }\n            }\n        },\n        yaxis: {\n            title: {\n                text: \"Sales\",\n                style: {\n                    color: \"#64748B\"\n                }\n            },\n            labels: {\n                style: {\n                    colors: \"#64748B\",\n                    fontSize: \"12px\"\n                }\n            }\n        },\n        fill: {\n            opacity: 1\n        },\n        tooltip: {\n            y: {\n                formatter: function(val) {\n                    return val + \" sales\";\n                }\n            }\n        }\n    };\n    const packageSalesSeries = [\n        {\n            name: \"Sales\",\n            data: brandDistribution.filter((b)=>b && typeof b.sales === \"number\").map((b)=>b.sales)\n        }\n    ];\n    // // Chart options for package distribution\n    // const packageDistributionOptions = {\n    //   chart: {\n    //     type: 'donut',\n    //     sparkline: {\n    //       enabled: false,\n    //     },\n    //   },\n    //   colors: ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B'],\n    //   labels: ['Basic Hosting', 'Standard Hosting', 'Premium Hosting', 'Enterprise Hosting'],\n    //   legend: {\n    //     position: 'bottom',\n    //     fontSize: '14px',\n    //     markers: {\n    //       radius: 12,\n    //     },\n    //   },\n    //   dataLabels: {\n    //     enabled: false,\n    //   },\n    //   plotOptions: {\n    //     pie: {\n    //       donut: {\n    //         size: '65%',\n    //         labels: {\n    //           show: true,\n    //           total: {\n    //             show: true,\n    //             label: 'Total',\n    //             formatter: function (w) {\n    //               return w.globals.seriesTotals.reduce((a, b) => a + b, 0);\n    //             },\n    //           },\n    //         },\n    //       },\n    //     },\n    //   },\n    // };\n    // const packageDistributionSeries = [42, 38, 25, 18];\n    const packageDistributionOptions = {\n        chart: {\n            type: \"donut\",\n            sparkline: {\n                enabled: false\n            }\n        },\n        colors: [\n            \"#3B82F6\",\n            \"#8B5CF6\",\n            \"#10B981\",\n            \"#F59E0B\",\n            \"#EF4444\",\n            \"#6366F1\",\n            \"#F472B6\",\n            \"#FBBF24\"\n        ],\n        labels: brandDistribution.map((b)=>b.brandName),\n        legend: {\n            position: \"bottom\",\n            fontSize: \"14px\",\n            markers: {\n                radius: 12\n            }\n        },\n        dataLabels: {\n            enabled: false\n        },\n        plotOptions: {\n            pie: {\n                donut: {\n                    size: \"65%\",\n                    labels: {\n                        show: true,\n                        total: {\n                            show: true,\n                            label: \"Total\",\n                            formatter: function(w) {\n                                return w.globals.seriesTotals.reduce((a, b)=>a + b, 0);\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    };\n    const packageDistributionSeries = brandDistribution.map((b)=>b.packageCount);\n    // Chart options for SSL certificates\n    const sslCertificatesOptions = {\n        chart: {\n            type: \"line\",\n            toolbar: {\n                show: false\n            },\n            sparkline: {\n                enabled: false\n            }\n        },\n        colors: [\n            \"#10B981\",\n            \"#F59E0B\",\n            \"#EF4444\"\n        ],\n        dataLabels: {\n            enabled: false\n        },\n        stroke: {\n            curve: \"smooth\",\n            width: 2\n        },\n        xaxis: {\n            categories: [\n                \"Jan\",\n                \"Feb\",\n                \"Mar\",\n                \"Apr\",\n                \"May\",\n                \"Jun\"\n            ],\n            labels: {\n                style: {\n                    colors: \"#64748B\",\n                    fontSize: \"12px\"\n                }\n            }\n        },\n        yaxis: {\n            labels: {\n                style: {\n                    colors: \"#64748B\",\n                    fontSize: \"12px\"\n                }\n            }\n        },\n        legend: {\n            position: \"top\",\n            horizontalAlign: \"right\",\n            fontSize: \"14px\",\n            markers: {\n                radius: 12\n            }\n        },\n        grid: {\n            borderColor: \"#E2E8F0\",\n            strokeDashArray: 4\n        }\n    };\n    const sslCertificatesSeries = [\n        {\n            name: \"Active\",\n            data: [\n                25,\n                29,\n                32,\n                35,\n                38,\n                42\n            ]\n        },\n        {\n            name: \"Expiring Soon\",\n            data: [\n                5,\n                4,\n                6,\n                3,\n                7,\n                5\n            ]\n        },\n        {\n            name: \"Expired\",\n            data: [\n                2,\n                1,\n                3,\n                2,\n                1,\n                2\n            ]\n        }\n    ];\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 407,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        className: \"text-blue-600 hover:text-blue-700\",\n                        children: \"Try again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 408,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                lineNumber: 406,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n            lineNumber: 405,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-gradient-to-r from-blue-600 to-indigo-600 p-6 rounded-xl shadow-lg text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-between items-start md:items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                variant: \"h4\",\n                                className: \"text-white mb-2\",\n                                children: [\n                                    \"Welcome back, \",\n                                    user?.firstName || \"Admin\",\n                                    \"!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                className: \"text-blue-100 max-w-2xl\",\n                                children: [\n                                    \"Here\",\n                                    \"'\",\n                                    \"s an overview of your hosting platform\",\n                                    \"'\",\n                                    \"s performance. Manage your packages, orders, and support tickets from this dashboard.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 424,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                    lineNumber: 423,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                lineNumber: 422,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"p-6 rounded-xl shadow-md hover:shadow-lg transition-all border-l-4 border-blue-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            variant: \"small\",\n                                            className: \"text-gray-600 mb-1\",\n                                            children: \"Total Users\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            variant: \"h4\",\n                                            className: \"font-bold\",\n                                            children: stats.users\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-1 text-green-500 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"12.5% increase\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 w-12 rounded-full bg-blue-500/10 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-6 w-6 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 437,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"p-6 rounded-xl shadow-md hover:shadow-lg transition-all border-l-4 border-purple-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            variant: \"small\",\n                                            className: \"text-gray-600 mb-1\",\n                                            children: \"Packages\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            variant: \"h4\",\n                                            className: \"font-bold\",\n                                            children: stats.packages\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-1 text-green-500 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"8.3% increase\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 w-12 rounded-full bg-purple-500/10 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-6 w-6 text-purple-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"p-6 rounded-xl shadow-md hover:shadow-lg transition-all border-l-4 border-green-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            variant: \"small\",\n                                            className: \"text-gray-600 mb-1\",\n                                            children: \"Total Orders\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            variant: \"h4\",\n                                            className: \"font-bold\",\n                                            children: stats.orders\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-1 text-green-500 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"15.2% increase\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 w-12 rounded-full bg-green-500/10 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-6 w-6 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                            lineNumber: 470,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 469,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"p-6 rounded-xl shadow-md hover:shadow-lg transition-all border-l-4 border-amber-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            variant: \"small\",\n                                            className: \"text-gray-600 mb-1\",\n                                            children: \"Revenue\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            variant: \"h4\",\n                                            className: \"font-bold\",\n                                            children: [\n                                                \"MAD \",\n                                                stats.revenue?.toFixed(2).toLocaleString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-1 text-green-500 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"18.7% increase\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 w-12 rounded-full bg-amber-500/10 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-6 w-6 text-amber-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                            lineNumber: 486,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 485,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                lineNumber: 436,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"p-6 rounded-xl shadow-md col-span-1 md:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"h6\",\n                                        className: \"font-semibold\",\n                                        children: \"Revenue Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            className: \"flex items-center gap-4 relative\",\n                                            layout: true,\n                                            children: [\n                                                \"weekly\",\n                                                \"monthly\",\n                                                \"yearly\"\n                                            ].map((view)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setRevenueView(view),\n                                                    className: `relative px-3 py-1 font-medium transition-colors duration-200 ${revenueView === view ? \"text-blue-600\" : \"text-gray-500 hover:text-blue-500\"} bg-transparent outline-none`,\n                                                    style: {\n                                                        background: \"none\",\n                                                        border: \"none\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"capitalize\",\n                                                            children: view\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        revenueView === view && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                                            layoutId: \"underline\",\n                                                            className: \"absolute left-0 right-0 -bottom-1 h-0.5 rounded-md bg-blue-600\",\n                                                            transition: {\n                                                                type: \"spring\",\n                                                                stiffness: 500,\n                                                                damping: 30,\n                                                                duration: 0.5\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, view, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-80\",\n                                children:  false && /*#__PURE__*/ 0\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 533,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 505,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"p-6 rounded-xl shadow-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"h6\",\n                                        className: \"font-semibold\",\n                                        children: \"Package Distribution\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center gap-2\",\n                                        children: categories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartSuspence, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                                    className: \"flex items-center justify-between w-48 px-4 py-2 border border-gray-200 rounded-md shadow-sm bg-white text-gray-700 font-medium focus:outline-none\",\n                                                    onClick: ()=>setShowCategoryDropdown((prev)=>!prev),\n                                                    whileTap: {\n                                                        scale: 0.97\n                                                    },\n                                                    type: \"button\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: categories.find((cat)=>cat._id === selectedCategory)?.name || \"Select Category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showCategoryDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 45\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 81\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showCategoryDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.ul, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: -8\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    exit: {\n                                                        opacity: 0,\n                                                        y: -8\n                                                    },\n                                                    className: \"absolute z-10 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg\",\n                                                    children: categories.map((cat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: `w-full text-left px-4 py-2 hover:bg-blue-50 transition ${selectedCategory === cat._id ? \"bg-blue-100 text-blue-700 font-semibold\" : \"\"}`,\n                                                                onClick: ()=>{\n                                                                    setSelectedCategory(cat._id);\n                                                                    setShowCategoryDropdown(false);\n                                                                },\n                                                                type: \"button\",\n                                                                children: cat.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, cat._id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 547,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: loadingDistribution ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartSuspence, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Chart, {\n                                    options: packageDistributionOptions,\n                                    series: packageDistributionSeries,\n                                    type: \"donut\",\n                                    height: 300\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 594,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-2 mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outlined\",\n                                        size: \"sm\",\n                                        className: \"flex items-center justify-center\",\n                                        onClick: ()=>window.location.href = \"/admin/packages\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Manage Packages\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outlined\",\n                                        size: \"sm\",\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"View Details\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 606,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 546,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                lineNumber: 503,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"p-6 rounded-xl shadow-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                variant: \"h6\",\n                                className: \"font-semibold mb-4\",\n                                children: \"Hosting Package Sales\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 622,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-72\",\n                                children:  false && /*#__PURE__*/ 0\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 623,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end mt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"text\",\n                                    size: \"sm\",\n                                    className: \"text-blue-500\",\n                                    onClick: ()=>window.location.href = \"/admin/orders\",\n                                    children: \"View All Orders\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 633,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 621,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"p-6 rounded-xl shadow-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                variant: \"h6\",\n                                className: \"font-semibold mb-4\",\n                                children: \"SSL Certificates Status\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 641,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-72\",\n                                children:  false && /*#__PURE__*/ 0\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 642,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end mt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"text\",\n                                    size: \"sm\",\n                                    className: \"text-blue-500\",\n                                    onClick: ()=>window.location.href = \"/admin/ssl\",\n                                    children: \"Manage SSL Certificates\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 652,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 640,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                lineNumber: 620,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"p-6 rounded-xl shadow-md col-span-1 md:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"h6\",\n                                        className: \"font-semibold\",\n                                        children: \"Recent Orders\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"text\",\n                                        size: \"sm\",\n                                        className: \"text-primary\",\n                                        onClick: ()=>window.location.href = \"/admin/orders\",\n                                        children: \"View All\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 663,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Order ID\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Customer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 670,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: stats.recentOrders?.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600\",\n                                                            children: [\n                                                                \"#\",\n                                                                order.identifiant\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 682,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 flex items-center gap-0.5\",\n                                                            children: [\n                                                                \" \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 114\n                                                                }, this),\n                                                                \" \",\n                                                                order.customer\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: [\n                                                                order.amount.toFixed(2),\n                                                                \" \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: \"MAD\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 113\n                                                                }, this),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `px-2 inline-flex text-xs leading-5 font-semibold rounded-full\r\n                        ${order.status === \"completed\" ? \"bg-green-100 text-green-800\" : order.status === \"processing\" ? \"bg-blue-100 text-blue-800\" : \"bg-yellow-100 text-yellow-800\"}`,\n                                                                children: order.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 686,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 685,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500 flex items-center gap-0.5\",\n                                                            children: [\n                                                                \" \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 114\n                                                                }, this),\n                                                                \" \",\n                                                                order.date\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, order.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 669,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 668,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 662,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"p-6 rounded-xl shadow-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"h6\",\n                                        className: \"font-semibold\",\n                                        children: \"Support Tickets\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-500 text-white text-xs font-medium px-2.5 py-0.5 rounded-full\",\n                                        children: [\n                                            stats.support,\n                                            \" Open\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 702,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    stats.recentTickets?.map((ticket, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                            variant: \"small\",\n                                                            className: \"font-medium\",\n                                                            children: ticket.subject\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `text-xs px-2 py-0.5 rounded-full ${ticket.priority === \"High\" ? \"bg-red-100 text-red-800\" : ticket.priority === \"Medium\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-green-100 text-green-800\"}`,\n                                                            children: ticket.priority\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                            variant: \"small\",\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Status: \",\n                                                                ticket.status\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 722,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"text\",\n                                                            size: \"sm\",\n                                                            className: \"p-0 text-primary\",\n                                                            children: \"View\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 723,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 721,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 15\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outlined\",\n                                        fullWidth: true,\n                                        className: \"flex items-center justify-center mt-2\",\n                                        onClick: ()=>window.location.href = \"/admin/support\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Manage Support\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 728,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 709,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 701,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                lineNumber: 661,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6 rounded-xl shadow-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                        variant: \"h6\",\n                        className: \"font-semibold mb-4\",\n                        children: \"Quick Actions\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 738,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                        children: [\n                            {\n                                icon: _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                label: \"New Package\",\n                                color: \"bg-purple-500\",\n                                path: \"/admin/packages\"\n                            },\n                            {\n                                icon: _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                label: \"Manage Users\",\n                                color: \"bg-blue-500\",\n                                path: \"/admin/users\"\n                            },\n                            {\n                                icon: _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                label: \"Support Tickets\",\n                                color: \"bg-green-500\",\n                                path: \"/admin/support\"\n                            },\n                            {\n                                icon: _barrel_optimize_names_ArrowUpRight_BarChart2_ChartLine_ChevronDown_ChevronUp_DollarSign_Globe_MessageSquare_Package_ShoppingCart_Ticket_Timer_User_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                label: \"View Website\",\n                                color: \"bg-orange-500\",\n                                path: \"/\"\n                            }\n                        ].map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outlined\",\n                                className: \"flex items-center justify-center gap-2 p-4 h-auto\",\n                                onClick: ()=>window.location.href = action.path,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `${action.color} text-white p-2 rounded-full`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: action.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 746,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 739,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n                lineNumber: 737,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\admin\\\\page.jsx\",\n        lineNumber: 420,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin/page.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/config/constant.js":
/*!************************************!*\
  !*** ./src/app/config/constant.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_GOOGLE_MAP_KEY: () => (/* binding */ API_GOOGLE_MAP_KEY),\n/* harmony export */   BACKEND_URL: () => (/* binding */ BACKEND_URL),\n/* harmony export */   COOKIE_DOMAIN: () => (/* binding */ COOKIE_DOMAIN),\n/* harmony export */   FRONTEND_URL: () => (/* binding */ FRONTEND_URL),\n/* harmony export */   REACT_APP_GG_APP_ID: () => (/* binding */ REACT_APP_GG_APP_ID)\n/* harmony export */ });\nconst backendDev = \"http://localhost:5002\";\nconst frontendDev = \"http://localhost:3001\";\nconst backend = \"https://api.ztechengineering.com\";\nconst frontend = \"https://ztechengineering.com\";\nconst isProd = false;\nconst BACKEND_URL = isProd ? backend : backendDev;\nconst FRONTEND_URL = isProd ? frontend : frontendDev;\nconst COOKIE_DOMAIN = isProd ? \".ztechengineering.com\" : \"localhost\";\nconst REACT_APP_GG_APP_ID = \"480987384459-h3cie2vcshp09vphuvnshccqprco3fbo.apps.googleusercontent.com\";\nconst API_GOOGLE_MAP_KEY = \"AIzaSyA5pGy3UEKwbgjUY-72RmoR7npEq1b_uf0\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2NvbmZpZy9jb25zdGFudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBLE1BQU1BLGFBQWE7QUFDbkIsTUFBTUMsY0FBYztBQUVwQixNQUFNQyxVQUFVO0FBQ2hCLE1BQU1DLFdBQVc7QUFFakIsTUFBTUMsU0FBUztBQUNSLE1BQU1DLGNBQWNELFNBQVNGLFVBQVVGLFdBQVc7QUFDbEQsTUFBTU0sZUFBZUYsU0FBU0QsV0FBV0YsWUFBWTtBQUNyRCxNQUFNTSxnQkFBZ0JILFNBQVMsMEJBQTBCLFlBQVk7QUFFckUsTUFBTUksc0JBQ1gsMkVBQTJFO0FBQ3RFLE1BQU1DLHFCQUFxQiwwQ0FBMEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vc3JjL2FwcC9jb25maWcvY29uc3RhbnQuanM/YjE2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBiYWNrZW5kRGV2ID0gXCJodHRwOi8vbG9jYWxob3N0OjUwMDJcIjtcclxuY29uc3QgZnJvbnRlbmREZXYgPSBcImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMVwiO1xyXG5cclxuY29uc3QgYmFja2VuZCA9IFwiaHR0cHM6Ly9hcGkuenRlY2hlbmdpbmVlcmluZy5jb21cIjtcclxuY29uc3QgZnJvbnRlbmQgPSBcImh0dHBzOi8venRlY2hlbmdpbmVlcmluZy5jb21cIjtcclxuXHJcbmNvbnN0IGlzUHJvZCA9IGZhbHNlO1xyXG5leHBvcnQgY29uc3QgQkFDS0VORF9VUkwgPSBpc1Byb2QgPyBiYWNrZW5kIDogYmFja2VuZERldjtcclxuZXhwb3J0IGNvbnN0IEZST05URU5EX1VSTCA9IGlzUHJvZCA/IGZyb250ZW5kIDogZnJvbnRlbmREZXY7XHJcbmV4cG9ydCBjb25zdCBDT09LSUVfRE9NQUlOID0gaXNQcm9kID8gXCIuenRlY2hlbmdpbmVlcmluZy5jb21cIiA6IFwibG9jYWxob3N0XCI7XHJcblxyXG5leHBvcnQgY29uc3QgUkVBQ1RfQVBQX0dHX0FQUF9JRCA9XHJcbiAgXCI0ODA5ODczODQ0NTktaDNjaWUydmNzaHAwOXZwaHV2bnNoY2NxcHJjbzNmYm8uYXBwcy5nb29nbGV1c2VyY29udGVudC5jb21cIjtcclxuZXhwb3J0IGNvbnN0IEFQSV9HT09HTEVfTUFQX0tFWSA9IFwiQUl6YVN5QTVwR3kzVUVLd2JnalVZLTcyUm1vUjducEVxMWJfdWYwXCI7XHJcbiJdLCJuYW1lcyI6WyJiYWNrZW5kRGV2IiwiZnJvbnRlbmREZXYiLCJiYWNrZW5kIiwiZnJvbnRlbmQiLCJpc1Byb2QiLCJCQUNLRU5EX1VSTCIsIkZST05URU5EX1VSTCIsIkNPT0tJRV9ET01BSU4iLCJSRUFDVF9BUFBfR0dfQVBQX0lEIiwiQVBJX0dPT0dMRV9NQVBfS0VZIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/config/constant.js\n");

/***/ }),

/***/ "(ssr)/./src/app/context/AuthContext.jsx":
/*!*****************************************!*\
  !*** ./src/app/context/AuthContext.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_authService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/authService */ \"(ssr)/./src/app/services/authService.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n// Create the Auth Context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Create a Provider Component\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cartCount, setCartCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Check if user exists in localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            await checkAuth();\n            setLoading(false);\n        };\n        initializeAuth();\n    }, []);\n    const checkAuth = async ()=>{\n        setLoading(true);\n        try {\n            const response = await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].checkAuth();\n            const updatedUser = response.data.user;\n            console.log(\"Fetched user:\", updatedUser);\n            // Update only if the data is different\n            if (JSON.stringify(updatedUser) !== localStorage.getItem(\"user\")) {\n                localStorage.setItem(\"user\", JSON.stringify(updatedUser));\n                document.cookie = `role=${updatedUser.role}; max-age=604800; path=/; secure`;\n            }\n            setUser(updatedUser);\n            return updatedUser;\n        } catch (err) {\n            console.error(\"Auth check failed:\", err);\n            setUser(null);\n            localStorage.removeItem(\"user\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Handle authentication error\n    const handleAuthError = (error)=>{\n        const message = error.response?.data?.message || \"An unexpected error occurred\";\n        console.error(error);\n        return error;\n    };\n    // Login function\n    const login = async (credentials)=>{\n        setLoading(true);\n        try {\n            const loginRes = await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].login(credentials);\n            const userData = loginRes.data.user;\n            setUser(userData);\n            // Store user in localStorage\n            localStorage.setItem(\"user\", JSON.stringify(userData));\n            document.cookie = `role=${userData.role}; max-age=604800; path=/; secure`;\n            // Check for pending cart items\n            const pendingItemJson = localStorage.getItem(\"pendingCartItem\");\n            if (pendingItemJson) {\n                try {\n                    // We'll handle this in a separate function after login completes\n                    // Just mark that we have a pending item\n                    loginRes.data.hasPendingCartItem = true;\n                } catch (cartError) {\n                    console.error(\"Error handling pending cart item:\", cartError);\n                }\n            }\n            return loginRes;\n        } catch (error) {\n            const detailedError = {\n                status: error.response?.status,\n                data: error.response?.data\n            };\n            throw detailedError;\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Logout function\n    const logout = async ()=>{\n        setLoading(true);\n        try {\n            await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].logout();\n            // Clear user from localStorage\n            localStorage.removeItem(\"user\");\n            // Clear cookies on logout\n            document.cookie = \"role=; Max-Age=0; path:/\";\n            document.cookie = \"refresh_token=; Max-Age=0; path=/;\";\n            document.cookie = \"token=; Max-Age=0; path=/;\";\n            setUser(null);\n            router.refresh();\n            router.push(\"/auth/login\"); // Redirect to login page after logout\n        } catch (error) {\n            console.log(\"Logout error:\", error.response?.data?.message || error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Compute if user is authenticated\n    const isAuthenticated = !!user;\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            user,\n            loading,\n            login,\n            logout,\n            checkAuth,\n            cartCount,\n            setCartCount,\n            isAuthenticated\n        }), [\n        user,\n        loading,\n        cartCount,\n        checkAuth,\n        isAuthenticated\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\context\\\\AuthContext.jsx\",\n        lineNumber: 143,\n        columnNumber: 10\n    }, undefined);\n};\n// Custom hook for using AuthContext\nconst useAuth = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/context/AuthContext.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/global-error.jsx":
/*!**********************************!*\
  !*** ./src/app/global-error.jsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GlobalError)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction GlobalError({ error, reset }) {\n    console.error(\"Global error caught:\", error);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-8 rounded-lg shadow-md max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-red-600 mb-4\",\n                            children: \"Something went wrong!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\global-error.jsx\",\n                            lineNumber: 14,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: [\n                                \"We\",\n                                \"'\",\n                                \"re sorry, but there was an unexpected error. Our team has been notified.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\global-error.jsx\",\n                            lineNumber: 15,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 p-4 rounded mb-6 overflow-auto max-h-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: \"text-sm text-gray-800\",\n                                children: error.message || \"Unknown error\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\global-error.jsx\",\n                                lineNumber: 19,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\global-error.jsx\",\n                            lineNumber: 18,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>reset(),\n                            className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition duration-150\",\n                            children: \"Try again\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\global-error.jsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\global-error.jsx\",\n                    lineNumber: 13,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\global-error.jsx\",\n                lineNumber: 12,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\global-error.jsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\global-error.jsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/global-error.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/helpers/helpers.js":
/*!************************************!*\
  !*** ./src/app/helpers/helpers.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccountNotVerifiedCode: () => (/* binding */ AccountNotVerifiedCode),\n/* harmony export */   arrayToMatrix: () => (/* binding */ arrayToMatrix),\n/* harmony export */   calculatePrice: () => (/* binding */ calculatePrice),\n/* harmony export */   clearAllCookies: () => (/* binding */ clearAllCookies),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   generateObjectFromHeaders: () => (/* binding */ generateObjectFromHeaders),\n/* harmony export */   getColorBasedOnState: () => (/* binding */ getColorBasedOnState),\n/* harmony export */   getDateDropdown: () => (/* binding */ getDateDropdown),\n/* harmony export */   getFormatNotificationTime: () => (/* binding */ getFormatNotificationTime),\n/* harmony export */   getFormattedPlans: () => (/* binding */ getFormattedPlans),\n/* harmony export */   getLocalizedContent: () => (/* binding */ getLocalizedContent),\n/* harmony export */   getMaxDiscount: () => (/* binding */ getMaxDiscount),\n/* harmony export */   getMonthName: () => (/* binding */ getMonthName),\n/* harmony export */   getPercent: () => (/* binding */ getPercent),\n/* harmony export */   getPriorityColor: () => (/* binding */ getPriorityColor),\n/* harmony export */   getRole: () => (/* binding */ getRole),\n/* harmony export */   getStatusIcon: () => (/* binding */ getStatusIcon),\n/* harmony export */   getTypeFromHeader: () => (/* binding */ getTypeFromHeader),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   maskRIB: () => (/* binding */ maskRIB),\n/* harmony export */   roundThis: () => (/* binding */ roundThis),\n/* harmony export */   sanitizeInput: () => (/* binding */ sanitizeInput),\n/* harmony export */   setServerMedia: () => (/* binding */ setServerMedia),\n/* harmony export */   setUserCookies: () => (/* binding */ setUserCookies),\n/* harmony export */   truncateString: () => (/* binding */ truncateString)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-cookie */ \"(ssr)/./node_modules/universal-cookie/esm/index.mjs\");\n/* harmony import */ var _config_constant__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../config/constant */ \"(ssr)/./src/app/config/constant.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n\n\n\n\nconst cookies = new react_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"]();\nconst AccountNotVerifiedCode = 2001;\n// export const generateObjectFromHeaders = (headers) => {\n//     const obj = {};\n//     headers.map((header) => header.name).map((h) => obj[h] = '')\n//     return obj\n// }\nconst generateObjectFromHeaders = (headers)=>{\n    const obj = {};\n    headers.map((header)=>(header.parent ? header.parent + \".\" : \"\") + header.name).forEach((h)=>{\n        const splitArr = h.split(\".\");\n        if (splitArr.length > 1) {\n            if (!obj[splitArr[0]]) obj[splitArr[0]] = {};\n            obj[splitArr[0]][splitArr[1]] = \"\";\n        } else obj[h] = \"\";\n    });\n    return obj;\n};\nconst getTypeFromHeader = (headers, attrName)=>{\n    const res = headers.filter((header)=>header.name == attrName);\n    if (res.length) return res[0].type;\n    return null;\n};\nconst isAuthenticated = ()=>{\n    return cookies.get(\"token\");\n};\nconst getUser = ()=>{\n    if (cookies.get(\"user\")) {\n        return cookies.get(\"user\");\n    }\n    return null;\n};\nconst getRole = ()=>{\n    if (cookies.get(\"role\")) {\n        return cookies.get(\"role\");\n    }\n    return null;\n};\nconst clearAllCookies = async ()=>{\n    try {\n        const allCookies = cookies.getAll(); // Get all cookies\n        Object.keys(allCookies).forEach((cookieName)=>{\n            cookies.remove(cookieName, {\n                path: \"/\"\n            });\n        });\n        console.log(\"ALL Cookies are cleared\");\n    } catch (error) {\n        console.log(error);\n    }\n};\nconst setServerMedia = (imageUrl)=>{\n    if (!imageUrl) return imageUrl;\n    if (imageUrl.indexOf(\"https://\") != -1 || imageUrl.indexOf(\"http://\") != -1) return imageUrl;\n    // return \"http://localhost:5000\" + imageUrl;\n    return _config_constant__WEBPACK_IMPORTED_MODULE_1__.BACKEND_URL + imageUrl;\n};\nconst setUserCookies = async (user)=>{\n    await cookies.remove(\"user\", {\n        path: \"/\"\n    });\n    await cookies.remove(\"Language\", {\n        path: \"/\"\n    });\n    await cookies.remove(\"role\", {\n        path: \"/\"\n    });\n    // Setting cookies with a global path\n    await cookies.set(\"user\", JSON.stringify(user), {\n        path: \"/\"\n    });\n    await cookies.set(\"Language\", user.favoriteLang, {\n        path: \"/\"\n    });\n    await cookies.set(\"role\", user.role, {\n        path: \"/\"\n    });\n};\nconst getPercent = (regularPrice, sellingPrice)=>{\n    return Math.ceil(100 * (regularPrice - sellingPrice) / regularPrice);\n};\nconst getFormatNotificationTime = (t, createdAt)=>{\n    const currentDate = new Date();\n    const createdDate = new Date(createdAt);\n    const timeDifference = currentDate - createdDate;\n    const seconds = Math.floor(timeDifference / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n    const days = Math.floor(hours / 24);\n    const months = Math.floor(days / 30);\n    if (months > 0) {\n        return `${months} ${t(\"months\")}${months > 1 ? t(\"plural\") : \"\"} ${t(\"ago\")}`;\n    } else if (days > 0) {\n        return `${days} ${t(\"day\")}${days > 1 ? t(\"plural\") : \"\"} ${t(\"ago\")}`;\n    } else if (hours > 0) {\n        return `${hours} ${t(\"hour\")}${hours > 1 ? t(\"plural\") : \"\"} ${t(\"ago\")}`;\n    } else if (minutes > 0) {\n        return `${minutes} ${t(\"minute\")}${minutes > 1 ? t(\"plural\") : \"\"} ${t(\"ago\")}`;\n    } else {\n        return `${t(\"just_now\")}`;\n    }\n};\nfunction arrayToMatrix(arr, columns) {\n    const matrix = [];\n    for(let i = 0; i < arr.length; i += columns){\n        matrix.push(arr.slice(i, i + columns));\n    }\n    return matrix;\n}\nconst formatDate = (t, inputDate)=>{\n    const currentDate = new Date();\n    const date = new Date(inputDate);\n    // Check if it's today\n    if (date.getDate() === currentDate.getDate() && date.getMonth() === currentDate.getMonth() && date.getFullYear() === currentDate.getFullYear()) {\n        // Display time (hours and minutes) for today\n        return `${date.getHours()}:${String(date.getMinutes()).padStart(2, \"0\")}`;\n    }\n    // Check if it's yesterday\n    const yesterday = new Date(currentDate);\n    yesterday.setDate(currentDate.getDate() - 1);\n    if (date.getDate() === yesterday.getDate() && date.getMonth() === yesterday.getMonth() && date.getFullYear() === yesterday.getFullYear()) {\n        // Display 'Yesterday' and time for yesterday\n        return `${t(\"yesterday\")}, ${date.getHours()}:${String(date.getMinutes()).padStart(2, \"0\")}`;\n    }\n    // Display the full date for other days\n    return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}, ${date.getHours()}:${String(date.getMinutes()).padStart(2, \"0\")}`;\n};\nfunction truncateString(str, maxLength) {\n    if (str.length > maxLength) {\n        return str.slice(0, maxLength) + \"...\";\n    } else {\n        return str;\n    }\n}\nconst sanitizeInput = (input, inputType)=>{\n    let sanitizedInput;\n    if (inputType === \"BillToName\") {\n        // Allow only letters (including Arabic and French) and spaces\n        sanitizedInput = input.replace(/[^\\p{L}\\s]/gu, \"\");\n    } else if (inputType === \"address\") {\n        // Allow only letters (including Arabic and French), numbers, spaces, and specified punctuation\n        sanitizedInput = input.replace(/[^\\p{L}0-9\\s\\-.,'_]/gu, \"\");\n    } else {\n        sanitizedInput = input.replace(/[^\\w\\.\\-@]/g, \"\");\n    }\n    return sanitizedInput;\n};\nconst getDateDropdown = (createdAt)=>{\n    const currentDate = new Date();\n    const creationDate = new Date(createdAt);\n    const getYears = ()=>{\n        const startYear = creationDate.getFullYear();\n        const currentYear = currentDate.getFullYear();\n        const years = [];\n        for(let year = startYear; year <= currentYear; year++){\n            years.push(year);\n        }\n        return years;\n    };\n    const getMonths = (year)=>{\n        const months = [];\n        const startYear = creationDate.getFullYear();\n        const currentYear = currentDate.getFullYear();\n        const startMonth = startYear === year ? creationDate.getMonth() + 1 : 1;\n        const endMonth = year === currentYear ? currentDate.getMonth() + 1 : 12;\n        for(let month = startMonth; month <= endMonth; month++){\n            months.push(month);\n        }\n        return months;\n    };\n    return {\n        getYears,\n        getMonths\n    };\n};\nconst getMonthName = (monthNumber, lang = \"default\")=>{\n    const date = new Date();\n    date.setMonth(monthNumber - 1); // JavaScript months are 0-based (0 = January, 11 = December)\n    return date.toLocaleString(lang, {\n        month: \"long\"\n    });\n};\nconst maskRIB = (rib)=>{\n    // if (!rib) return '';\n    // const firstPart = rib.slice(0, 3);\n    // const lastPart = rib.slice(-4);\n    // return (\n    //     <>\n    //         <span className=\"mr-1\">{firstPart}</span>\n    //         <span className=\"\">• • • • • •</span>\n    //         <span className=\"ml-1\">{lastPart}</span>\n    //     </>\n    // );\n    if (!rib) return \"\";\n    const firstPart = rib.slice(0, 6); // Adjust slicing if needed\n    const lastPart = rib.slice(-4);\n    return `${firstPart} • • • • • • ${lastPart}`;\n};\nfunction getColorBasedOnState(state) {\n    switch(state){\n        case \"NOT_VERIFIED\":\n            return \"gray\";\n        case \"VERIFIED\":\n            return \"green\";\n        case \"SUSPENDED\":\n            return \"yellow\";\n        case \"BLOCKED\":\n            return \"red\";\n        default:\n            return \"gray\";\n    }\n}\n// Helper function to get status icon\nconst getStatusIcon = (status)=>{\n    switch(status){\n        case \"resolved\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-5 w-5 text-green-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\helpers\\\\helpers.js\",\n                lineNumber: 260,\n                columnNumber: 14\n            }, undefined);\n        case \"closed\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-5 w-5 text-red-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\helpers\\\\helpers.js\",\n                lineNumber: 262,\n                columnNumber: 14\n            }, undefined);\n        case \"in_progress\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-5 w-5 text-yellow-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\helpers\\\\helpers.js\",\n                lineNumber: 264,\n                columnNumber: 14\n            }, undefined);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5 text-blue-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\helpers\\\\helpers.js\",\n                lineNumber: 266,\n                columnNumber: 14\n            }, undefined);\n    }\n};\n// Helper function to get priority color\nconst getPriorityColor = (priority)=>{\n    switch(priority){\n        case \"urgent\":\n            return \"bg-red-100 text-red-800\";\n        case \"high\":\n            return \"bg-orange-100 text-orange-800\";\n        case \"medium\":\n            return \"bg-yellow-100 text-yellow-800\";\n        default:\n            return \"bg-green-100 text-green-800\";\n    }\n};\nconst roundThis = (value)=>{\n    return Math.round(Number(value));\n};\nconst calculatePrice = (plan, billingPeriod)=>{\n    const basePrice = plan.price;\n    const yearlyDiscount = plan.discounts?.find((d)=>d.period === 12)?.percentage || 0;\n    const monthlyDiscount = plan.discounts?.find((d)=>d.period === 1)?.percentage || 0;\n    if (billingPeriod === \"yearly\") {\n        const discountedYearlyTotal = basePrice * (1 - yearlyDiscount / 100);\n        return {\n            price: discountedYearlyTotal,\n            regularPrice: basePrice\n        };\n    } else {\n        return {\n            price: basePrice * (1 - monthlyDiscount / 100),\n            regularPrice: basePrice\n        };\n    }\n};\nconst getFormattedPlans = (hostingPacks, billingPeriod)=>{\n    return hostingPacks.map((plan)=>{\n        const pricing = calculatePrice(plan, billingPeriod);\n        return {\n            ...plan,\n            price: pricing.price.toFixed(2),\n            regularPrice: pricing.regularPrice.toFixed(2)\n        };\n    });\n};\nconst getMaxDiscount = (hostingPacks)=>{\n    return hostingPacks.reduce((max, plan)=>{\n        const yearlyDiscount = plan.discounts?.find((d)=>d.period === 12)?.percentage || 0;\n        return Math.max(max, yearlyDiscount);\n    }, 0);\n};\nconst getLocalizedContent = (content, field, locale)=>{\n    if (!content || !field) return \"\"; // Ensure content and field are defined\n    const localizedField = locale === \"fr\" ? `${field}_fr` : field;\n    return content[localizedField] ?? content[field] ?? \"\"; // Fallback if localized field is missing\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/helpers/helpers.js\n");

/***/ }),

/***/ "(ssr)/./src/app/hook/useIsMobile.js":
/*!*************************************!*\
  !*** ./src/app/hook/useIsMobile.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsMobile: () => (/* binding */ useIsMobile)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useIsMobile auto */ \nfunction useIsMobile() {\n    // Initialize with null to avoid hydration mismatch\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Set initial value\n        setIsMobile(window.innerWidth < 768);\n        // Create handler for window resize\n        const handleResize = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        // Add event listener\n        window.addEventListener(\"resize\", handleResize);\n        // Clean up\n        return ()=>{\n            window.removeEventListener(\"resize\", handleResize);\n        };\n    }, []);\n    // Return false during SSR, then the actual value after hydration\n    return isMobile === null ? false : isMobile;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/hook/useIsMobile.js\n");

/***/ }),

/***/ "(ssr)/./src/app/lib/apiService.js":
/*!***********************************!*\
  !*** ./src/app/lib/apiService.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _axiosInstance__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./axiosInstance */ \"(ssr)/./src/app/lib/axiosInstance.js\");\n\nconst apiService = {\n    get: (url, params = {})=>_axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n            params\n        }),\n    post: (url, data = {}, config = {})=>_axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, data, config),\n    put: (url, data = {}, config = {})=>_axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(url, data, config),\n    delete: (url, config = {})=>_axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(url, config)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2xpYi9hcGlTZXJ2aWNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRDO0FBRTVDLE1BQU1DLGFBQWE7SUFDZkMsS0FBSyxDQUFDQyxLQUFLQyxTQUFTLENBQUMsQ0FBQyxHQUFLSixzREFBYUEsQ0FBQ0UsR0FBRyxDQUFDQyxLQUFLO1lBQUVDO1FBQU87SUFFM0RDLE1BQU0sQ0FBQ0YsS0FBS0csT0FBTyxDQUFDLENBQUMsRUFBRUMsU0FBUyxDQUFDLENBQUMsR0FBS1Asc0RBQWFBLENBQUNLLElBQUksQ0FBQ0YsS0FBS0csTUFBTUM7SUFFckVDLEtBQUssQ0FBQ0wsS0FBS0csT0FBTyxDQUFDLENBQUMsRUFBRUMsU0FBUyxDQUFDLENBQUMsR0FBS1Asc0RBQWFBLENBQUNRLEdBQUcsQ0FBQ0wsS0FBS0csTUFBTUM7SUFFbkVFLFFBQVEsQ0FBQ04sS0FBS0ksU0FBUyxDQUFDLENBQUMsR0FBS1Asc0RBQWFBLENBQUNTLE1BQU0sQ0FBQ04sS0FBS0k7QUFDNUQ7QUFFQSxpRUFBZU4sVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9zcmMvYXBwL2xpYi9hcGlTZXJ2aWNlLmpzPzYxZDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zSW5zdGFuY2UgZnJvbSAnLi9heGlvc0luc3RhbmNlJztcclxuXHJcbmNvbnN0IGFwaVNlcnZpY2UgPSB7XHJcbiAgICBnZXQ6ICh1cmwsIHBhcmFtcyA9IHt9KSA9PiBheGlvc0luc3RhbmNlLmdldCh1cmwsIHsgcGFyYW1zIH0pLFxyXG5cclxuICAgIHBvc3Q6ICh1cmwsIGRhdGEgPSB7fSwgY29uZmlnID0ge30pID0+IGF4aW9zSW5zdGFuY2UucG9zdCh1cmwsIGRhdGEsIGNvbmZpZyksXHJcblxyXG4gICAgcHV0OiAodXJsLCBkYXRhID0ge30sIGNvbmZpZyA9IHt9KSA9PiBheGlvc0luc3RhbmNlLnB1dCh1cmwsIGRhdGEsIGNvbmZpZyksXHJcblxyXG4gICAgZGVsZXRlOiAodXJsLCBjb25maWcgPSB7fSkgPT4gYXhpb3NJbnN0YW5jZS5kZWxldGUodXJsLCBjb25maWcpLFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgYXBpU2VydmljZTtcclxuIl0sIm5hbWVzIjpbImF4aW9zSW5zdGFuY2UiLCJhcGlTZXJ2aWNlIiwiZ2V0IiwidXJsIiwicGFyYW1zIiwicG9zdCIsImRhdGEiLCJjb25maWciLCJwdXQiLCJkZWxldGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/lib/apiService.js\n");

/***/ }),

/***/ "(ssr)/./src/app/lib/axiosInstance.js":
/*!**************************************!*\
  !*** ./src/app/lib/axiosInstance.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _config_constant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../config/constant */ \"(ssr)/./src/app/config/constant.js\");\n\n\nconst axiosInstance = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: _config_constant__WEBPACK_IMPORTED_MODULE_0__.BACKEND_URL,\n    timeout: 10000,\n    withCredentials: true\n});\naxiosInstance.interceptors.response.use((response)=>response, (error)=>{\n    if (error.response?.status === 401) {\n        console.log(\"from axios instance, try to redirect user to login\");\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (axiosInstance);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2xpYi9heGlvc0luc3RhbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEwQjtBQUN1QjtBQUVqRCxNQUFNRSxnQkFBZ0JGLDZDQUFLQSxDQUFDRyxNQUFNLENBQUM7SUFDakNDLFNBQVNILHlEQUFXQTtJQUNwQkksU0FBUztJQUNUQyxpQkFBaUI7QUFDbkI7QUFFQUosY0FBY0ssWUFBWSxDQUFDQyxRQUFRLENBQUNDLEdBQUcsQ0FDckMsQ0FBQ0QsV0FBYUEsVUFDZCxDQUFDRTtJQUNDLElBQUlBLE1BQU1GLFFBQVEsRUFBRUcsV0FBVyxLQUFLO1FBQ2xDQyxRQUFRQyxHQUFHLENBQUM7SUFDZDtJQUNBLE9BQU9DLFFBQVFDLE1BQU0sQ0FBQ0w7QUFDeEI7QUFHRixpRUFBZVIsYUFBYUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9zcmMvYXBwL2xpYi9heGlvc0luc3RhbmNlLmpzPzAwMGUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zIGZyb20gXCJheGlvc1wiO1xyXG5pbXBvcnQgeyBCQUNLRU5EX1VSTCB9IGZyb20gXCIuLi9jb25maWcvY29uc3RhbnRcIjtcclxuXHJcbmNvbnN0IGF4aW9zSW5zdGFuY2UgPSBheGlvcy5jcmVhdGUoe1xyXG4gIGJhc2VVUkw6IEJBQ0tFTkRfVVJMLFxyXG4gIHRpbWVvdXQ6IDEwMDAwLFxyXG4gIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxufSk7XHJcblxyXG5heGlvc0luc3RhbmNlLmludGVyY2VwdG9ycy5yZXNwb25zZS51c2UoXHJcbiAgKHJlc3BvbnNlKSA9PiByZXNwb25zZSxcclxuICAoZXJyb3IpID0+IHtcclxuICAgIGlmIChlcnJvci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDEpIHtcclxuICAgICAgY29uc29sZS5sb2coXCJmcm9tIGF4aW9zIGluc3RhbmNlLCB0cnkgdG8gcmVkaXJlY3QgdXNlciB0byBsb2dpblwiKTtcclxuICAgIH1cclxuICAgIHJldHVybiBQcm9taXNlLnJlamVjdChlcnJvcik7XHJcbiAgfVxyXG4pO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgYXhpb3NJbnN0YW5jZTtcclxuIl0sIm5hbWVzIjpbImF4aW9zIiwiQkFDS0VORF9VUkwiLCJheGlvc0luc3RhbmNlIiwiY3JlYXRlIiwiYmFzZVVSTCIsInRpbWVvdXQiLCJ3aXRoQ3JlZGVudGlhbHMiLCJpbnRlcmNlcHRvcnMiLCJyZXNwb25zZSIsInVzZSIsImVycm9yIiwic3RhdHVzIiwiY29uc29sZSIsImxvZyIsIlByb21pc2UiLCJyZWplY3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/lib/axiosInstance.js\n");

/***/ }),

/***/ "(ssr)/./src/app/services/authService.js":
/*!*****************************************!*\
  !*** ./src/app/services/authService.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_apiService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/apiService */ \"(ssr)/./src/app/lib/apiService.js\");\n\nconst authService = {\n    register: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/auth/register\", data, {\n            withCredentials: true\n        }),\n    cartRegister: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/auth/cartRegister\", data, {\n            withCredentials: true\n        }),\n    login: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/auth/login\", data, {\n            withCredentials: true\n        }),\n    checkAuth: ()=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/auth/checkAuth`, {\n            withCredentials: true\n        }),\n    logout: ()=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/auth/logout\", {\n            withCredentials: true\n        }),\n    refreshToken: ()=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/auth/refresh-token\", {}, {\n            withCredentials: true\n        }),\n    forgotPassword: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/auth/forgot-password\", data, {\n            withCredentials: true\n        }),\n    resetPassword: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/auth/reset-password\", data, {\n            withCredentials: true\n        })\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (authService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/services/authService.js\n");

/***/ }),

/***/ "(ssr)/./src/components/avatar/UserAvatar.jsx":
/*!**********************************************!*\
  !*** ./src/components/avatar/UserAvatar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Home_LogOut_User_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Home,LogOut,User,UserCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Home_LogOut_User_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Home,LogOut,User,UserCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Home_LogOut_User_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Home,LogOut,User,UserCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Home_LogOut_User_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Home,LogOut,User,UserCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Home_LogOut_User_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Home,LogOut,User,UserCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _app_helpers_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../app/helpers/helpers */ \"(ssr)/./src/app/helpers/helpers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_context_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../app/context/AuthContext */ \"(ssr)/./src/app/context/AuthContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst UserAvatar = ({ user, size = \"md\", showName = false, className = \"\" })=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const menuRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { logout } = (0,_app_context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    // Size classes mapping\n    const sizeClasses = {\n        sm: \"w-6 h-6\",\n        md: \"w-8 h-8\",\n        lg: \"w-10 h-10\",\n        xl: \"w-12 h-12\"\n    };\n    // Icon size mapping\n    const iconSizes = {\n        sm: \"w-4 h-4\",\n        md: \"w-6 h-6\",\n        lg: \"w-8 h-8\",\n        xl: \"w-10 h-10\"\n    };\n    // Get the appropriate size class\n    const avatarSize = sizeClasses[size] || sizeClasses.md;\n    const iconSize = iconSizes[size] || iconSizes.md;\n    // Handle click outside to close menu\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (menuRef.current && !menuRef.current.contains(event.target)) {\n                setIsMenuOpen(false);\n            }\n        };\n        if (isMenuOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isMenuOpen\n    ]);\n    // Handle logout\n    const handleLogout = async ()=>{\n        try {\n            await logout();\n            setIsMenuOpen(false);\n            router.push(\"/auth/login\");\n        } catch (error) {\n            console.error(\"Logout failed:\", error);\n        }\n    };\n    // Navigate to profile\n    const goToProfile = ()=>{\n        router.push(\"/client/profile\");\n        setIsMenuOpen(false);\n    };\n    // Navigate to client website\n    const goToClientWebsite = ()=>{\n        router.push(\"/\");\n        setIsMenuOpen(false);\n    };\n    // Toggle menu\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative flex items-center space-x-2 ${className}`,\n        ref: menuRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: toggleMenu,\n                className: \"flex items-center space-x-2 focus:outline-none\",\n                \"aria-expanded\": isMenuOpen,\n                \"aria-haspopup\": \"true\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${avatarSize} rounded-full flex items-center justify-center overflow-hidden ${!user?.photo ? \"bg-blue-500 text-white\" : \"\"}`,\n                        children: user?.photo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: (0,_app_helpers_helpers__WEBPACK_IMPORTED_MODULE_2__.setServerMedia)(user.photo),\n                            alt: `${user.firstName || \"\"} ${user.lastName || \"\"}`,\n                            className: \"w-full h-full object-cover\",\n                            onError: (e)=>{\n                                e.target.onerror = null;\n                                e.target.style.display = \"none\";\n                                e.target.parentNode.classList.add(\"bg-blue-500\", \"text-white\");\n                                e.target.parentNode.innerHTML = `<div class=\"${iconSize}\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-user\"><circle cx=\"12\" cy=\"8\" r=\"5\"/><path d=\"M20 21a8 8 0 0 0-16 0\"/></svg></div>`;\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Home_LogOut_User_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: iconSize\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, undefined),\n                    showName && user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700 hidden md:inline-block\",\n                                children: `${user?.firstName || \"\"} ${user?.lastName || \"\"}` || \"User\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Home_LogOut_User_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 top-full z-10 w-48 bg-white rounded-md shadow-lg py-1 border border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-2 border-b border-gray-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-900 truncate\",\n                                children: `${user?.firstName || \"\"} ${user?.lastName || \"\"}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 truncate\",\n                                children: user?.email\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: goToProfile,\n                        className: \"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Home_LogOut_User_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"mr-3 h-4 w-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Profile\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: goToClientWebsite,\n                        className: \"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Home_LogOut_User_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"mr-3 h-4 w-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Client Website\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-gray-100 my-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleLogout,\n                        className: \"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Home_LogOut_User_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"mr-3 h-4 w-4 text-red-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Sign Out\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\avatar\\\\UserAvatar.jsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UserAvatar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/avatar/UserAvatar.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!***********************************************!*\
  !*** ./src/components/home/<USER>
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bell_Check_Clock_Info_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Check,Clock,Info!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Check_Clock_Info_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Check,Clock,Info!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Check_Clock_Info_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Check,Clock,Info!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Check_Clock_Info_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Check,Clock,Info!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Notifications = ()=>{\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const notificationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Sample notifications data - replace with your actual data source\n    const notifications = [\n        {\n            id: 1,\n            title: \"New message received\",\n            content: \"You have a new message from support team\",\n            time: \"5 minutes ago\",\n            read: false\n        },\n        {\n            id: 2,\n            title: \"Order completed\",\n            content: \"Your order #12345 has been processed\",\n            time: \"2 hours ago\",\n            read: false\n        },\n        {\n            id: 3,\n            title: \"System update\",\n            content: \"The system will be updated on Friday at 2 AM\",\n            time: \"1 day ago\",\n            read: true\n        },\n        {\n            id: 4,\n            title: \"Welcome to ZTech\",\n            content: \"Thank you for joining our platform\",\n            time: \"3 days ago\",\n            read: true\n        }\n    ];\n    // Set mounted state to avoid hydration issues\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsMounted(true);\n    }, []);\n    // Handle click outside to close dropdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isMounted) return;\n        const handleClickOutside = (event)=>{\n            if (notificationRef.current && !notificationRef.current.contains(event.target)) {\n                setIsOpen(false);\n            }\n        };\n        if (isOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isOpen,\n        isMounted\n    ]);\n    // Count unread notifications\n    const unreadCount = notifications.filter((notification)=>!notification.read).length;\n    // Toggle notification panel\n    const toggleNotifications = ()=>{\n        setIsOpen(!isOpen);\n    };\n    // If not mounted yet, render a simplified version to avoid hydration mismatch\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"relative p-2 rounded-full hover:bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Check_Clock_Info_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-5 h-5 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Get icon based on notification type\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Check_Clock_Info_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 64,\n                    columnNumber: 30\n                }, undefined);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Check_Clock_Info_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-4 h-4 text-amber-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 65,\n                    columnNumber: 30\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Check_Clock_Info_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 66,\n                    columnNumber: 23\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: notificationRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"relative p-2 rounded-full hover:bg-gray-100 border border-gray-100\",\n                \"aria-label\": \"Notifications\",\n                onClick: toggleNotifications,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Check_Clock_Info_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-5 h-5 text-gray-600 \"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, undefined),\n                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute top-0 right-0 flex size-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute inline-flex h-full w-full animate-ping rounded-full bg-red-400 opacity-75\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"relative inline-flex size-2 rounded-full bg-red-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-custom-heavy border border-gray-200 z-50 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border-b border-gray-200 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-700\",\n                                    children: \"Notifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, undefined),\n                                unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-0.5 bg-primary/10 text-primary text-xs font-medium rounded-full\",\n                                    children: [\n                                        unreadCount,\n                                        \" new\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto hide-scrollbar\",\n                        children: notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-gray-100\",\n                            children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `p-3 hover:bg-gray-50 transition-colors cursor-pointer ${!notification.read ? \"bg-blue-50/50\" : \"\"}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 mt-0.5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-8 h-8 rounded-full flex items-center justify-center ${!notification.read ? \"bg-primary/10\" : \"bg-gray-100\"}`,\n                                                    children: getNotificationIcon(notification.type)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: `text-sm font-medium truncate ${!notification.read ? \"text-primary\" : \"text-gray-800\"}`,\n                                                                children: notification.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                                lineNumber: 121,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 whitespace-nowrap ml-2\",\n                                                                children: notification.time\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: notification.content\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, notification.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                            lineNumber: 102,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-8 px-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Check_Clock_Info_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-6 h-6 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 mb-1\",\n                                    children: \"No notifications yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: [\n                                        \"We\",\n                                        \"'\",\n                                        \"ll notify you when something arrives\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                            lineNumber: 137,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-2 border-t border-gray-200 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-sm text-primary hover:text-blue-700 font-medium transition-colors py-1\",\n                                    children: \"Mark all as read\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-sm text-gray-500 hover:text-gray-700 transition-colors py-1\",\n                                    children: \"View all\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Notifications);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/services/adminService.js":
/*!**************************************!*\
  !*** ./src/services/adminService.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminService: () => (/* binding */ adminService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../app/lib/apiService */ \"(ssr)/./src/app/lib/apiService.js\");\n\nconst adminService = {\n    // Get dashboard statistics\n    getDashboardStats: async ()=>{\n        return await _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/admin/dashboard/stats\");\n    },\n    // Get all users with pagination\n    getUsers: async (page = 1, limit = 10, search = \"\")=>{\n        return await _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/admin/users?page=${page}&limit=${limit}&search=${search}`);\n    },\n    // Add a new user\n    addUser: async (userData)=>{\n        return await _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/admin/users\", userData);\n    },\n    // Update a user\n    updateUser: async (userId, userData)=>{\n        return await _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`/admin/users/${userId}`, userData);\n    },\n    // Delete a user\n    deleteUser: async (userId)=>{\n        return await _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(`/admin/users/${userId}`);\n    },\n    // Get all packages\n    getPackages: async ()=>{\n        return await _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/admin/packages\");\n    },\n    // Add a new package\n    addPackage: async (packageData)=>{\n        return await _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/admin/packages\", packageData);\n    },\n    // Update a package\n    updatePackage: async (packageId, packageData)=>{\n        return await _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`/admin/packages/${packageId}`, packageData);\n    },\n    // Delete a package\n    deletePackage: async (packageId)=>{\n        return await _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(`/admin/packages/${packageId}`);\n    },\n    // Get all orders with pagination\n    getOrders: async (page = 1, limit = 10, status = \"\")=>{\n        return await _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/admin/orders?page=${page}&limit=${limit}&status=${status}`);\n    },\n    // Update order status\n    updateOrderStatus: async (orderId, status)=>{\n        return await _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`/admin/orders/${orderId}/status`, {\n            status\n        });\n    },\n    // Get all categories\n    getCategories: async ()=>{\n        return await _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/admin/categories\");\n    },\n    // Get all specifications\n    getSpecifications: async ()=>{\n        return await _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/admin/specs\");\n    },\n    // Add a new specification\n    addSpecification: async (specData)=>{\n        return await _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/admin/specs\", specData);\n    },\n    // Update a specification\n    updateSpecification: async (specData)=>{\n        return await _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/admin/update-spec\", specData);\n    },\n    getPackageDistribution: async (categoryId)=>{\n        return await _app_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/admin/dashboard/package-distribution?categoryId=${categoryId}`);\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (adminService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/adminService.js\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"afd3d017e5d9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/MGYxMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImFmZDNkMDE3ZTVkOVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"afd3d017e5d9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/ZjkyYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImFmZDNkMDE3ZTVkOVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/admin/layout.jsx":
/*!**********************************!*\
  !*** ./src/app/admin/layout.jsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Work\ztech_new_env\ztech_dev\frontend\src\app\admin\layout.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/admin/page.jsx":
/*!********************************!*\
  !*** ./src/app/admin/page.jsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Work\ztech_new_env\ztech_dev\frontend\src\app\admin\page.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/context/AuthContext.jsx":
/*!*****************************************!*\
  !*** ./src/app/context/AuthContext.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Work\ztech_new_env\ztech_dev\frontend\src\app\context\AuthContext.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Work\ztech_new_env\ztech_dev\frontend\src\app\context\AuthContext.jsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Work\ztech_new_env\ztech_dev\frontend\src\app\context\AuthContext.jsx#useAuth`);


/***/ }),

/***/ "(rsc)/./src/app/global-error.jsx":
/*!**********************************!*\
  !*** ./src/app/global-error.jsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Work\ztech_new_env\ztech_dev\frontend\src\app\global-error.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.jsx":
/*!****************************!*\
  !*** ./src/app/layout.jsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./context/AuthContext */ \"(rsc)/./src/app/context/AuthContext.jsx\");\n/* harmony import */ var _react_oauth_google__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-oauth/google */ \"(rsc)/./node_modules/@react-oauth/google/dist/index.esm.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n\n\n\n// import { GoogleTagManager } from '@next/third-parties/google'\n\n\n\nasync function RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"ZtechEngineering | Web & Mobile Development, Cloud Hosting in Morocco\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: \"web development Morocco, mobile applications Morocco, cloud hosting Morocco, digital agency, ZtechEngineering, website creation, cloud solutions, custom development, web mobile, PWA, digital transformation, web agency Morocco\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: \"ZtechEngineering | Web & Mobile Development, Cloud Hosting in Morocco\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: \"ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:url\",\n                        content: \"https://ztechengineering.com/\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        content: \"https://ztechengineering.com/images/home/<USER>"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:site_name\",\n                        content: \"Ztechengineering\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"article:publisher\",\n                        content: \"https://web.facebook.com/profile.php?id=61551999353576&_rdc=1&_rdr\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"Organization\",\n                                \"name\": \"ZtechEngineering\",\n                                \"url\": \"https://ztechengineering.com/\",\n                                \"logo\": \"https://ztechengineering.com/images/home/<USER>",\n                                \"sameAs\": [\n                                    \"https://web.facebook.com/profile.php?id=61551999353576&_rdc=1&_rdr\",\n                                    \"https://www.youtube.com/@ztechengineering/\",\n                                    \"https://www.instagram.com/ztechengineering\",\n                                    \"https://www.linkedin.com/company/ztechengineering\"\n                                ]\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"robots\",\n                        content: \"index, follow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.png\",\n                        type: \"image/x-icon\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"google-site-verification\",\n                        content: \"RokYIbdh-kKoq7cMq7qJURkC43dc7JgI3ojch4CL0RQ\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"alternate\",\n                        hrefLang: \"fr\",\n                        href: \"https://ztechengineering.com/fr\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"alternate\",\n                        hrefLang: \"en\",\n                        href: \"https://ztechengineering.com/en\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        strategy: \"worker\",\n                        src: `https://www.googletagmanager.com/gtm.js?id=GTM-WBVG4FCK`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:locale\",\n                        content: \"en_US\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:locale:alternate\",\n                        content: \"fr_FR\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:card\",\n                        content: \"summary_large_image\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:title\",\n                        content: \"ZtechEngineering | Web & Mobile Development, Cloud Hosting in Morocco\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:description\",\n                        content: \"ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:image\",\n                        content: \"https://ztechengineering.com/images/home/<USER>"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:site\",\n                        content: \"@ztechengineering\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:creator\",\n                        content: \"@ztechengineering\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        \"http-equiv\": \"Content-Language\",\n                        content: \"en\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"language\",\n                        content: \"English\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noscript\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                            src: \"https://www.googletagmanager.com/ns.html?id=GTM-WBVG4FCK\",\n                            height: \"0\",\n                            width: \"0\",\n                            style: {\n                                display: \"none\",\n                                visibility: \"hidden\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_oauth_google__WEBPACK_IMPORTED_MODULE_4__.GoogleOAuthProvider, {\n                        clientId: process.env.GOOGLE_OAUTH_API_KEY,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\layout.jsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.jsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.jsx":
/*!*****************************!*\
  !*** ./src/app/loading.jsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Loader!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/loader.js\");\n\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            className: \"h-8 w-8 text-blue-600 animate-spin\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\loading.jsx\",\n            lineNumber: 6,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\loading.jsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvYWRpbmcuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXFDO0FBRXRCLFNBQVNDO0lBQ3BCLHFCQUNLLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNoQiw0RUFBQ0gsa0ZBQU1BO1lBQUNHLFdBQVU7Ozs7Ozs7Ozs7O0FBRzFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL3NyYy9hcHAvbG9hZGluZy5qc3g/YWMzNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBMb2FkZXIgfSBmcm9tICdsdWNpZGUtcmVhY3QnXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgIDxMb2FkZXIgY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LWJsdWUtNjAwIGFuaW1hdGUtc3BpblwiIC8+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKVxyXG59Il0sIm5hbWVzIjpbIkxvYWRlciIsIkxvYWRpbmciLCJkaXYiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.jsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.js":
/*!******************************!*\
  !*** ./src/app/not-found.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.js\");\n\nfunction NotFound() {\n    (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect)(\"/404\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL25vdC1mb3VuZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEyQztBQUU1QixTQUFTQztJQUN0QkQseURBQVFBLENBQUM7QUFDWCIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9zcmMvYXBwL25vdC1mb3VuZC5qcz84NzY0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJlZGlyZWN0IH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE5vdEZvdW5kKCkge1xyXG4gIHJlZGlyZWN0KCcvNDA0Jyk7XHJcbn1cclxuIl0sIm5hbWVzIjpbInJlZGlyZWN0IiwiTm90Rm91bmQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@material-tailwind","vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/@floating-ui","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/tailwind-merge","vendor-chunks/@motionone","vendor-chunks/lucide-react","vendor-chunks/tslib","vendor-chunks/motion-dom","vendor-chunks/popmotion","vendor-chunks/prop-types","vendor-chunks/tabbable","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/@react-oauth","vendor-chunks/form-data","vendor-chunks/universal-cookie","vendor-chunks/@heroicons","vendor-chunks/react-is","vendor-chunks/asynckit","vendor-chunks/aria-hidden","vendor-chunks/style-value-types","vendor-chunks/combined-stream","vendor-chunks/@emotion","vendor-chunks/deepmerge","vendor-chunks/mime-types","vendor-chunks/framesync","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/object-assign","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/material-ripple-effects","vendor-chunks/motion-utils","vendor-chunks/classnames","vendor-chunks/hey-listen","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.jsx&appDir=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();